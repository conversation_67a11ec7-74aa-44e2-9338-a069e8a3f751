"""
Configuration management system for the Discord bot.
Centralizes all configuration loading and validation.
"""

import os
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from utils.logger import startup_info, warning, error

@dataclass
class DatabaseConfig:
    """Database configuration"""
    host: str
    port: int
    user: str
    password: str
    database: str
    pool_size: int
    connection_timeout: int

@dataclass
class RedisConfig:
    """Redis configuration"""
    host: str
    port: int
    password: Optional[str]
    decode_responses: bool
    socket_timeout: int

@dataclass
class BotConfig:
    """Bot configuration"""
    token: str
    prefix: str
    dev_server_id: Optional[int]
    dev_role_id: Optional[int]
    premium_role_id: Optional[int]
    support_server: str
    debug_mode: bool
    verbose_logging: bool

@dataclass
class SecurityConfig:
    """Security configuration"""
    max_commands_per_minute: int
    max_validation_failures: int
    suspicious_user_threshold: int
    rate_limit_window: int
    enable_security_audit: bool

class ConfigManager:
    """Centralized configuration management"""
    
    def __init__(self):
        self.database: Optional[DatabaseConfig] = None
        self.redis: Optional[RedisConfig] = None
        self.bot: Optional[BotConfig] = None
        self.security: Optional[SecurityConfig] = None
        self._loaded = False
    
    def load_config(self) -> bool:
        """
        Load all configuration from environment variables.
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            startup_info("Config", "Loading configuration from environment")
            
            # Load database config
            self.database = self._load_database_config()
            
            # Load Redis config
            self.redis = self._load_redis_config()
            
            # Load bot config
            self.bot = self._load_bot_config()
            
            # Load security config
            self.security = self._load_security_config()
            
            # Validate all configs
            if not self._validate_config():
                return False
            
            self._loaded = True
            startup_info("Config", "Configuration loaded successfully")
            return True
            
        except Exception as e:
            error("Failed to load configuration", e)
            return False
    
    def _load_database_config(self) -> DatabaseConfig:
        """Load database configuration"""
        return DatabaseConfig(
            host=self._get_required_env('DB_HOST'),
            port=int(self._get_env('DB_PORT', '3306')),
            user=self._get_required_env('DB_USER'),
            password=self._get_required_env('DB_PASSWORD'),
            database=self._get_required_env('DB_NAME'),
            pool_size=int(self._get_env('DB_POOL_SIZE', '15')),
            connection_timeout=int(self._get_env('DB_CONNECTION_TIMEOUT', '30'))
        )
    
    def _load_redis_config(self) -> RedisConfig:
        """Load Redis configuration"""
        return RedisConfig(
            host=self._get_env('REDIS_HOST', 'localhost'),
            port=int(self._get_env('REDIS_PORT', '6379')),
            password=self._get_env('REDIS_PASSWORD'),
            decode_responses=self._get_env('REDIS_DECODE_RESPONSES', 'true').lower() == 'true',
            socket_timeout=int(self._get_env('REDIS_SOCKET_TIMEOUT', '2'))
        )
    
    def _load_bot_config(self) -> BotConfig:
        """Load bot configuration"""
        return BotConfig(
            token=self._get_required_env('DISCORD_TOKEN'),
            prefix=self._get_env('DISCORD_PREFIX', ','),
            dev_server_id=self._get_optional_int('DEV_SERVER_ID'),
            dev_role_id=self._get_optional_int('DEV_ROLE_ID'),
            premium_role_id=self._get_optional_int('PREMIUM_ROLE_ID'),
            support_server=self._get_env('SUPPORT_SERVER', 'https://discord.gg/ge8gxgVE9U'),
            debug_mode=self._get_env('DEBUG_MODE', 'false').lower() == 'true',
            verbose_logging=self._get_env('VERBOSE_LOGGING', 'false').lower() == 'true'
        )
    
    def _load_security_config(self) -> SecurityConfig:
        """Load security configuration"""
        return SecurityConfig(
            max_commands_per_minute=int(self._get_env('MAX_COMMANDS_PER_MINUTE', '30')),
            max_validation_failures=int(self._get_env('MAX_VALIDATION_FAILURES', '10')),
            suspicious_user_threshold=int(self._get_env('SUSPICIOUS_USER_THRESHOLD', '5')),
            rate_limit_window=int(self._get_env('RATE_LIMIT_WINDOW', '60')),
            enable_security_audit=self._get_env('ENABLE_SECURITY_AUDIT', 'true').lower() == 'true'
        )
    
    def _validate_config(self) -> bool:
        """Validate all configuration"""
        errors = []
        
        # Validate database config
        if not self.database.host:
            errors.append("Database host is required")
        if not (1 <= self.database.port <= 65535):
            errors.append("Database port must be between 1 and 65535")
        if self.database.pool_size < 1:
            errors.append("Database pool size must be at least 1")
        
        # Validate Redis config
        if not (1 <= self.redis.port <= 65535):
            errors.append("Redis port must be between 1 and 65535")
        
        # Validate bot config
        if not self.bot.token:
            errors.append("Discord token is required")
        if len(self.bot.prefix) > 10:
            errors.append("Bot prefix cannot be longer than 10 characters")
        
        # Validate security config
        if self.security.max_commands_per_minute < 1:
            errors.append("Max commands per minute must be at least 1")
        
        if errors:
            for err in errors:
                error(f"Configuration validation error: {err}")
            return False
        
        return True
    
    def _get_required_env(self, key: str) -> str:
        """Get required environment variable"""
        value = os.getenv(key)
        if not value:
            raise ValueError(f"Required environment variable {key} is not set")
        return value
    
    def _get_env(self, key: str, default: str = '') -> str:
        """Get environment variable with default"""
        return os.getenv(key, default)
    
    def _get_optional_int(self, key: str) -> Optional[int]:
        """Get optional integer environment variable"""
        value = os.getenv(key)
        if value:
            try:
                return int(value)
            except ValueError:
                warning(f"Invalid integer value for {key}: {value}")
        return None
    
    def is_loaded(self) -> bool:
        """Check if configuration is loaded"""
        return self._loaded
    
    def get_database_url(self) -> str:
        """Get database connection URL"""
        if not self.database:
            raise RuntimeError("Database config not loaded")
        
        return f"mysql://{self.database.user}:{self.database.password}@{self.database.host}:{self.database.port}/{self.database.database}"
    
    def get_redis_url(self) -> str:
        """Get Redis connection URL"""
        if not self.redis:
            raise RuntimeError("Redis config not loaded")
        
        auth = f":{self.redis.password}@" if self.redis.password else ""
        return f"redis://{auth}{self.redis.host}:{self.redis.port}"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary (for debugging)"""
        if not self._loaded:
            return {"error": "Configuration not loaded"}
        
        return {
            "database": {
                "host": self.database.host,
                "port": self.database.port,
                "database": self.database.database,
                "pool_size": self.database.pool_size,
                "connection_timeout": self.database.connection_timeout
                # Don't include sensitive data like passwords
            },
            "redis": {
                "host": self.redis.host,
                "port": self.redis.port,
                "decode_responses": self.redis.decode_responses,
                "socket_timeout": self.redis.socket_timeout
                # Don't include password
            },
            "bot": {
                "prefix": self.bot.prefix,
                "dev_server_id": self.bot.dev_server_id,
                "support_server": self.bot.support_server,
                "debug_mode": self.bot.debug_mode,
                "verbose_logging": self.bot.verbose_logging
                # Don't include token
            },
            "security": {
                "max_commands_per_minute": self.security.max_commands_per_minute,
                "max_validation_failures": self.security.max_validation_failures,
                "suspicious_user_threshold": self.security.suspicious_user_threshold,
                "rate_limit_window": self.security.rate_limit_window,
                "enable_security_audit": self.security.enable_security_audit
            }
        }

# Global configuration manager instance
config_manager = ConfigManager()

def load_config() -> bool:
    """Load configuration (convenience function)"""
    return config_manager.load_config()

def get_config() -> ConfigManager:
    """Get configuration manager instance"""
    return config_manager
