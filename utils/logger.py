"""
Centralized logging system for the Discord bot.
Provides structured logging with different levels and proper formatting.
"""

import logging
import logging.handlers
import os
from datetime import datetime
from typing import Optional
from utils.constants import (
    LOG_FORMAT, LOG_DATE_FORMAT, MAX_LOG_FILE_SIZE_MB, 
    LOG_BACKUP_COUNT, DEBUG_MODE, VERBOSE_LOGGING
)

class BotLogger:
    """Centralized logging system for the bot"""
    
    def __init__(self, name: str = "blur_bot"):
        self.name = name
        self.logger = logging.getLogger(name)
        self._setup_logger()
    
    def _setup_logger(self):
        """Setup the logger with appropriate handlers and formatting"""
        # Clear any existing handlers
        self.logger.handlers.clear()
        
        # Set logging level
        if DEBUG_MODE:
            self.logger.setLevel(logging.DEBUG)
        elif VERBOSE_LOGGING:
            self.logger.setLevel(logging.INFO)
        else:
            self.logger.setLevel(logging.WARNING)
        
        # Create formatter
        formatter = logging.Formatter(
            fmt=LOG_FORMAT,
            datefmt=LOG_DATE_FORMAT
        )
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.INFO)
        self.logger.addHandler(console_handler)
        
        # File handler (rotating)
        try:
            # Create logs directory if it doesn't exist
            os.makedirs('logs', exist_ok=True)
            
            file_handler = logging.handlers.RotatingFileHandler(
                filename='logs/bot.log',
                maxBytes=MAX_LOG_FILE_SIZE_MB * 1024 * 1024,  # Convert MB to bytes
                backupCount=LOG_BACKUP_COUNT,
                encoding='utf-8'
            )
            file_handler.setFormatter(formatter)
            file_handler.setLevel(logging.DEBUG)
            self.logger.addHandler(file_handler)
            
            # Error file handler
            error_handler = logging.handlers.RotatingFileHandler(
                filename='logs/errors.log',
                maxBytes=MAX_LOG_FILE_SIZE_MB * 1024 * 1024,
                backupCount=LOG_BACKUP_COUNT,
                encoding='utf-8'
            )
            error_handler.setFormatter(formatter)
            error_handler.setLevel(logging.ERROR)
            self.logger.addHandler(error_handler)
            
        except Exception as e:
            print(f"Failed to setup file logging: {e}")
    
    def debug(self, message: str, **kwargs):
        """Log debug message"""
        self.logger.debug(message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """Log info message"""
        self.logger.info(message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message"""
        self.logger.warning(message, **kwargs)
    
    def error(self, message: str, exception: Optional[Exception] = None, **kwargs):
        """Log error message with optional exception"""
        if exception:
            self.logger.error(f"{message}: {exception}", exc_info=True, **kwargs)
        else:
            self.logger.error(message, **kwargs)
    
    def critical(self, message: str, exception: Optional[Exception] = None, **kwargs):
        """Log critical message with optional exception"""
        if exception:
            self.logger.critical(f"{message}: {exception}", exc_info=True, **kwargs)
        else:
            self.logger.critical(message, **kwargs)
    
    def database_error(self, operation: str, exception: Exception, **kwargs):
        """Log database-specific errors"""
        self.error(f"Database error in {operation}", exception, **kwargs)
    
    def command_error(self, command: str, user_id: int, guild_id: Optional[int], exception: Exception, **kwargs):
        """Log command-specific errors"""
        guild_info = f"guild:{guild_id}" if guild_id else "DM"
        self.error(f"Command error '{command}' by user:{user_id} in {guild_info}", exception, **kwargs)
    
    def security_warning(self, message: str, user_id: Optional[int] = None, guild_id: Optional[int] = None, **kwargs):
        """Log security-related warnings"""
        context = []
        if user_id:
            context.append(f"user:{user_id}")
        if guild_id:
            context.append(f"guild:{guild_id}")
        
        context_str = f" [{', '.join(context)}]" if context else ""
        self.warning(f"SECURITY: {message}{context_str}", **kwargs)
    
    def performance_warning(self, operation: str, duration: float, threshold: float = 1.0, **kwargs):
        """Log performance warnings for slow operations"""
        if duration > threshold:
            self.warning(f"PERFORMANCE: {operation} took {duration:.2f}s (threshold: {threshold}s)", **kwargs)
    
    def cache_info(self, operation: str, hit_rate: Optional[float] = None, **kwargs):
        """Log cache-related information"""
        if hit_rate is not None:
            self.info(f"CACHE: {operation} (hit rate: {hit_rate:.2%})", **kwargs)
        else:
            self.info(f"CACHE: {operation}", **kwargs)
    
    def startup_info(self, component: str, status: str, **kwargs):
        """Log startup information"""
        self.info(f"STARTUP: {component} - {status}", **kwargs)
    
    def guild_event(self, event: str, guild_id: int, details: str = "", **kwargs):
        """Log guild-related events"""
        message = f"GUILD: {event} in guild:{guild_id}"
        if details:
            message += f" - {details}"
        self.info(message, **kwargs)
    
    def moderation_action(self, action: str, moderator_id: int, target_id: int, guild_id: int, reason: str = "", **kwargs):
        """Log moderation actions"""
        message = f"MODERATION: {action} by user:{moderator_id} on user:{target_id} in guild:{guild_id}"
        if reason:
            message += f" - Reason: {reason}"
        self.info(message, **kwargs)

# Global logger instance
logger = BotLogger()

# Convenience functions for easy importing
def debug(message: str, **kwargs):
    logger.debug(message, **kwargs)

def info(message: str, **kwargs):
    logger.info(message, **kwargs)

def warning(message: str, **kwargs):
    logger.warning(message, **kwargs)

def error(message: str, exception: Optional[Exception] = None, **kwargs):
    logger.error(message, exception, **kwargs)

def critical(message: str, exception: Optional[Exception] = None, **kwargs):
    logger.critical(message, exception, **kwargs)

def database_error(operation: str, exception: Exception, **kwargs):
    logger.database_error(operation, exception, **kwargs)

def command_error(command: str, user_id: int, guild_id: Optional[int], exception: Exception, **kwargs):
    logger.command_error(command, user_id, guild_id, exception, **kwargs)

def security_warning(message: str, user_id: Optional[int] = None, guild_id: Optional[int] = None, **kwargs):
    logger.security_warning(message, user_id, guild_id, **kwargs)

def performance_warning(operation: str, duration: float, threshold: float = 1.0, **kwargs):
    logger.performance_warning(operation, duration, threshold, **kwargs)

def cache_info(operation: str, hit_rate: Optional[float] = None, **kwargs):
    logger.cache_info(operation, hit_rate, **kwargs)

def startup_info(component: str, status: str, **kwargs):
    logger.startup_info(component, status, **kwargs)

def guild_event(event: str, guild_id: int, details: str = "", **kwargs):
    logger.guild_event(event, guild_id, details, **kwargs)

def moderation_action(action: str, moderator_id: int, target_id: int, guild_id: int, reason: str = "", **kwargs):
    logger.moderation_action(action, moderator_id, target_id, guild_id, reason, **kwargs)
