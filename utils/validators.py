"""
Input validation utilities for the Discord bot.
Provides secure validation for all user inputs.
"""

import re
import discord
from typing import Optional, Union, Tuple
from urllib.parse import urlparse

class ValidationError(Exception):
    """Custom exception for validation errors"""
    pass

class Validators:
    """Collection of input validation methods"""
    
    # Discord ID pattern (15-21 digits)
    DISCORD_ID_PATTERN = re.compile(r'^[0-9]{15,21}$')
    
    # Discord message link pattern
    MESSAGE_LINK_PATTERN = re.compile(
        r'https://(?:ptb\.|canary\.)?discord(?:app)?\.com/channels/'
        r'(?P<guild_id>[0-9]{15,21}|@me)/'
        r'(?P<channel_id>[0-9]{15,21})/'
        r'(?P<message_id>[0-9]{15,21})'
    )
    
    # Custom emoji pattern
    CUSTOM_EMOJI_PATTERN = re.compile(r'<a?:[a-zA-Z0-9_]+:[0-9]{15,21}>')
    
    # Unicode emoji pattern (basic check)
    UNICODE_EMOJI_PATTERN = re.compile(r'[\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F1E0-\U0001F1FF\U00002600-\U000027BF\U0001F900-\U0001F9FF]+')
    
    @staticmethod
    def validate_discord_id(value: Union[str, int]) -> int:
        """
        Validate and convert Discord ID to integer.
        
        Args:
            value: The ID to validate (string or int)
            
        Returns:
            int: Valid Discord ID
            
        Raises:
            ValidationError: If ID is invalid
        """
        if isinstance(value, int):
            value = str(value)
        
        if not isinstance(value, str):
            raise ValidationError(f"Discord ID must be string or int, got {type(value)}")
        
        # Remove mention brackets if present
        value = value.strip('<@!#&>')
        
        if not Validators.DISCORD_ID_PATTERN.match(value):
            raise ValidationError(f"Invalid Discord ID format: {value}")
        
        discord_id = int(value)
        
        # Additional range check
        if discord_id < 10**14 or discord_id > 10**21:
            raise ValidationError(f"Discord ID out of valid range: {discord_id}")
        
        return discord_id
    
    @staticmethod
    def validate_message_link(link: str) -> Tuple[int, int, int]:
        """
        Validate Discord message link and extract IDs.
        
        Args:
            link: Discord message link
            
        Returns:
            Tuple[int, int, int]: (guild_id, channel_id, message_id)
            
        Raises:
            ValidationError: If link is invalid
        """
        if not isinstance(link, str):
            raise ValidationError(f"Message link must be string, got {type(link)}")
        
        match = Validators.MESSAGE_LINK_PATTERN.match(link.strip())
        if not match:
            raise ValidationError(f"Invalid Discord message link format: {link}")
        
        guild_id_str = match.group('guild_id')
        channel_id_str = match.group('channel_id')
        message_id_str = match.group('message_id')
        
        # Handle @me (DM) case
        if guild_id_str == '@me':
            raise ValidationError("DM message links are not supported")
        
        try:
            guild_id = Validators.validate_discord_id(guild_id_str)
            channel_id = Validators.validate_discord_id(channel_id_str)
            message_id = Validators.validate_discord_id(message_id_str)
        except ValidationError as e:
            raise ValidationError(f"Invalid IDs in message link: {e}")
        
        return guild_id, channel_id, message_id
    
    @staticmethod
    def validate_emoji(emoji: str) -> str:
        """
        Validate emoji (custom or unicode).
        
        Args:
            emoji: Emoji string to validate
            
        Returns:
            str: Valid emoji string
            
        Raises:
            ValidationError: If emoji is invalid
        """
        if not isinstance(emoji, str):
            raise ValidationError(f"Emoji must be string, got {type(emoji)}")
        
        emoji = emoji.strip()
        
        if not emoji:
            raise ValidationError("Emoji cannot be empty")
        
        # Check if it's a custom emoji
        if Validators.CUSTOM_EMOJI_PATTERN.match(emoji):
            return emoji
        
        # Check if it's a unicode emoji
        if Validators.UNICODE_EMOJI_PATTERN.match(emoji):
            return emoji
        
        # Check if it's a single unicode character that could be an emoji
        if len(emoji) == 1 and ord(emoji) > 127:
            return emoji
        
        raise ValidationError(f"Invalid emoji format: {emoji}")
    
    @staticmethod
    def validate_string_length(value: str, max_length: int, field_name: str = "field") -> str:
        """
        Validate string length.
        
        Args:
            value: String to validate
            max_length: Maximum allowed length
            field_name: Name of the field for error messages
            
        Returns:
            str: Valid string
            
        Raises:
            ValidationError: If string is invalid
        """
        if not isinstance(value, str):
            raise ValidationError(f"{field_name} must be string, got {type(value)}")
        
        if len(value) > max_length:
            raise ValidationError(f"{field_name} too long (max {max_length} characters)")
        
        return value
    
    @staticmethod
    def validate_button_color(color: str) -> str:
        """
        Validate button color.
        
        Args:
            color: Color name to validate
            
        Returns:
            str: Valid color name
            
        Raises:
            ValidationError: If color is invalid
        """
        if not isinstance(color, str):
            raise ValidationError(f"Button color must be string, got {type(color)}")
        
        valid_colors = ['blue', 'gray', 'grey', 'green', 'red']
        color_lower = color.lower().strip()
        
        if color_lower not in valid_colors:
            raise ValidationError(f"Invalid button color '{color}'. Valid colors: {', '.join(valid_colors)}")
        
        # Normalize grey/gray
        if color_lower == 'grey':
            return 'gray'
        
        return color_lower
    
    @staticmethod
    def validate_url(url: str) -> str:
        """
        Validate URL format.
        
        Args:
            url: URL to validate
            
        Returns:
            str: Valid URL
            
        Raises:
            ValidationError: If URL is invalid
        """
        if not isinstance(url, str):
            raise ValidationError(f"URL must be string, got {type(url)}")
        
        url = url.strip()
        
        if not url:
            raise ValidationError("URL cannot be empty")
        
        try:
            parsed = urlparse(url)
            if not parsed.scheme or not parsed.netloc:
                raise ValidationError(f"Invalid URL format: {url}")
        except Exception:
            raise ValidationError(f"Invalid URL format: {url}")
        
        return url
    
    @staticmethod
    def sanitize_sql_input(value: str) -> str:
        """
        Sanitize string input for SQL queries (additional safety).
        Note: This should be used alongside parameterized queries, not instead of them.
        
        Args:
            value: String to sanitize
            
        Returns:
            str: Sanitized string
        """
        if not isinstance(value, str):
            return str(value)
        
        # Remove null bytes and other dangerous characters
        value = value.replace('\x00', '')
        value = value.replace('\r', '')
        value = value.replace('\n', ' ')
        
        # Limit length to prevent DoS
        if len(value) > 1000:
            value = value[:1000]
        
        return value
