"""
Health check system for the Discord bot.
Monitors system health and provides diagnostic information.
"""

import discord
from discord.ext import commands
import time
import psutil
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from database.database import db
from utils.logger import warning, error, info
from utils.config_manager import get_config

class HealthChecker:
    """System health monitoring and diagnostics"""
    
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.start_time = time.time()
        self.last_health_check = None
        self.health_history: List[Dict[str, Any]] = []
        self.max_history = 100
    
    async def perform_health_check(self) -> Dict[str, Any]:
        """
        Perform comprehensive health check.
        
        Returns:
            Dict containing health status information
        """
        check_time = datetime.now()
        health_data = {
            'timestamp': check_time.isoformat(),
            'overall_status': 'healthy',
            'checks': {}
        }
        
        try:
            # Bot status check
            health_data['checks']['bot'] = await self._check_bot_status()
            
            # Database connectivity check
            health_data['checks']['database'] = await self._check_database()
            
            # Redis/Cache check
            health_data['checks']['cache'] = await self._check_cache()
            
            # System resources check
            health_data['checks']['system'] = self._check_system_resources()
            
            # Discord API status check
            health_data['checks']['discord_api'] = await self._check_discord_api()
            
            # Memory usage check
            health_data['checks']['memory'] = self._check_memory_usage()
            
            # Determine overall status
            health_data['overall_status'] = self._determine_overall_status(health_data['checks'])
            
            # Store in history
            self._store_health_data(health_data)
            
            self.last_health_check = check_time
            
        except Exception as e:
            error("Health check failed", e)
            health_data['overall_status'] = 'error'
            health_data['error'] = str(e)
        
        return health_data
    
    async def _check_bot_status(self) -> Dict[str, Any]:
        """Check bot connectivity and status"""
        try:
            latency = round(self.bot.latency * 1000, 2)
            uptime = time.time() - self.start_time
            
            status = 'healthy'
            if latency > 500:
                status = 'warning'
            if latency > 1000:
                status = 'critical'
            
            return {
                'status': status,
                'latency_ms': latency,
                'uptime_seconds': uptime,
                'guild_count': len(self.bot.guilds),
                'user_count': len(self.bot.users),
                'is_ready': self.bot.is_ready()
            }
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def _check_database(self) -> Dict[str, Any]:
        """Check database connectivity and performance"""
        try:
            start_time = time.time()
            
            # Simple connectivity test
            connection = db.mysql_pool.get_connection()
            cursor = connection.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            cursor.close()
            connection.close()
            
            response_time = (time.time() - start_time) * 1000
            
            status = 'healthy'
            if response_time > 100:
                status = 'warning'
            if response_time > 500:
                status = 'critical'
            
            return {
                'status': status,
                'response_time_ms': round(response_time, 2),
                'pool_size': db.mysql_pool.pool_size,
                'connected': True
            }
        except Exception as e:
            return {
                'status': 'error',
                'connected': False,
                'error': str(e)
            }
    
    async def _check_cache(self) -> Dict[str, Any]:
        """Check Redis/cache connectivity and performance"""
        try:
            start_time = time.time()
            
            # Test cache connectivity
            test_key = "health_check_test"
            test_value = {"test": True, "timestamp": time.time()}
            
            db.cache.set(test_key, test_value, ttl=60)
            retrieved = db.cache.get(test_key)
            db.cache.delete(test_key)
            
            response_time = (time.time() - start_time) * 1000
            
            status = 'healthy'
            if response_time > 50:
                status = 'warning'
            if response_time > 200:
                status = 'critical'
            
            return {
                'status': status,
                'response_time_ms': round(response_time, 2),
                'connected': db.cache.connected,
                'test_successful': retrieved is not None
            }
        except Exception as e:
            return {
                'status': 'error',
                'connected': False,
                'error': str(e)
            }
    
    def _check_system_resources(self) -> Dict[str, Any]:
        """Check system resource usage"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            status = 'healthy'
            if cpu_percent > 80 or memory.percent > 80 or disk.percent > 90:
                status = 'warning'
            if cpu_percent > 95 or memory.percent > 95 or disk.percent > 95:
                status = 'critical'
            
            return {
                'status': status,
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_available_gb': round(memory.available / (1024**3), 2),
                'disk_percent': disk.percent,
                'disk_free_gb': round(disk.free / (1024**3), 2)
            }
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def _check_discord_api(self) -> Dict[str, Any]:
        """Check Discord API responsiveness"""
        try:
            start_time = time.time()
            
            # Test API call
            await self.bot.fetch_user(self.bot.user.id)
            
            response_time = (time.time() - start_time) * 1000
            
            status = 'healthy'
            if response_time > 1000:
                status = 'warning'
            if response_time > 3000:
                status = 'critical'
            
            return {
                'status': status,
                'response_time_ms': round(response_time, 2),
                'api_accessible': True
            }
        except Exception as e:
            return {
                'status': 'error',
                'api_accessible': False,
                'error': str(e)
            }
    
    def _check_memory_usage(self) -> Dict[str, Any]:
        """Check bot memory usage"""
        try:
            import gc
            import sys
            
            # Force garbage collection
            gc.collect()
            
            # Get memory info
            process = psutil.Process()
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / (1024 * 1024)
            
            # Get object counts
            object_count = len(gc.get_objects())
            
            status = 'healthy'
            if memory_mb > 500:  # 500MB
                status = 'warning'
            if memory_mb > 1000:  # 1GB
                status = 'critical'
            
            return {
                'status': status,
                'memory_mb': round(memory_mb, 2),
                'object_count': object_count,
                'gc_collections': gc.get_count()
            }
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def _determine_overall_status(self, checks: Dict[str, Dict[str, Any]]) -> str:
        """Determine overall health status from individual checks"""
        statuses = [check.get('status', 'unknown') for check in checks.values()]
        
        if 'error' in statuses:
            return 'error'
        elif 'critical' in statuses:
            return 'critical'
        elif 'warning' in statuses:
            return 'warning'
        else:
            return 'healthy'
    
    def _store_health_data(self, health_data: Dict[str, Any]):
        """Store health data in history"""
        self.health_history.append(health_data)
        
        # Keep only recent history
        if len(self.health_history) > self.max_history:
            self.health_history = self.health_history[-self.max_history:]
    
    def get_health_summary(self) -> Dict[str, Any]:
        """Get summary of recent health status"""
        if not self.health_history:
            return {'status': 'no_data', 'message': 'No health data available'}
        
        recent_checks = self.health_history[-10:]  # Last 10 checks
        
        # Count status occurrences
        status_counts = {}
        for check in recent_checks:
            status = check.get('overall_status', 'unknown')
            status_counts[status] = status_counts.get(status, 0) + 1
        
        return {
            'current_status': self.health_history[-1].get('overall_status', 'unknown'),
            'last_check': self.last_health_check.isoformat() if self.last_health_check else None,
            'recent_status_counts': status_counts,
            'uptime_seconds': time.time() - self.start_time,
            'total_checks': len(self.health_history)
        }
    
    async def start_periodic_checks(self, interval_minutes: int = 5):
        """Start periodic health checks"""
        while True:
            try:
                await asyncio.sleep(interval_minutes * 60)
                health_data = await self.perform_health_check()
                
                # Log warnings/errors
                if health_data['overall_status'] in ['warning', 'critical', 'error']:
                    warning(f"Health check status: {health_data['overall_status']}")
                
            except Exception as e:
                error("Periodic health check failed", e)

# Global health checker instance (will be initialized in main.py)
health_checker: Optional[HealthChecker] = None

def get_health_checker() -> Optional[HealthChecker]:
    """Get the global health checker instance"""
    return health_checker

def initialize_health_checker(bot: commands.Bot) -> HealthChecker:
    """Initialize the global health checker"""
    global health_checker
    health_checker = HealthChecker(bot)
    return health_checker
