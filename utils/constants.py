"""
Constants used throughout the Discord bot.
Centralizes all magic numbers and configuration values.
"""

# Discord API Limits
DISCORD_ID_MIN_LENGTH = 15
DISCORD_ID_MAX_LENGTH = 21
DISCORD_ID_MIN_VALUE = 10**14
DISCORD_ID_MAX_VALUE = 10**21

# Message and Content Limits
MAX_EMBED_TITLE_LENGTH = 256
MAX_EMBED_DESCRIPTION_LENGTH = 4096
MAX_EMBED_FIELD_NAME_LENGTH = 256
MAX_EMBED_FIELD_VALUE_LENGTH = 1024
MAX_EMBED_FOOTER_LENGTH = 2048
MAX_EMBED_AUTHOR_NAME_LENGTH = 256
MAX_MESSAGE_CONTENT_LENGTH = 2000

# Database Field Limits
MAX_USERNAME_LENGTH = 255
MAX_DISPLAY_NAME_LENGTH = 255
MAX_PREFIX_LENGTH = 10
MAX_REASON_LENGTH = 1000

# Moderation Limits
MIN_TIMEOUT_SECONDS = 60  # 1 minute
MAX_TIMEOUT_SECONDS = 2419200  # 28 days
DEFAULT_TIMEOUT_SECONDS = 600  # 10 minutes

# Time Unit Conversions
SECONDS_PER_MINUTE = 60
SECONDS_PER_HOUR = 3600
SECONDS_PER_DAY = 86400
SECONDS_PER_WEEK = 604800

# Cache and Performance
DEFAULT_CACHE_TTL = 3600  # 1 hour
MAX_CACHED_ERRORS = 1000
ERROR_TTL_HOURS = 24
CACHE_CLEANUP_INTERVAL = 1800  # 30 minutes

# Pagination
DEFAULT_PAGINATION_TIMEOUT = 180  # 3 minutes
MAX_PAGINATION_PAGES = 50

# Button and UI
VALID_BUTTON_COLORS = ['blue', 'gray', 'grey', 'green', 'red']
BUTTON_TIMEOUT_SECONDS = 300  # 5 minutes
EMBED_BUILD_TIMEOUT = 300  # 5 minutes
COPY_BUTTON_TIMEOUT = 60  # 1 minute

# Database Connection
DEFAULT_DB_POOL_SIZE = 15
DB_CONNECTION_TIMEOUT = 30

# Rate Limiting
DEFAULT_COMMAND_COOLDOWN = 3  # seconds
PREMIUM_COMMAND_COOLDOWN = 1  # seconds

# File and Path Limits
MAX_FILE_SIZE_MB = 25
MAX_ATTACHMENT_COUNT = 10

# Role and Permission Limits
MAX_ROLES_PER_MESSAGE = 20
MAX_REACTION_ROLES_PER_MESSAGE = 20

# Search and Lookup
MAX_SEARCH_RESULTS = 25
MEMBER_SEARCH_LIMIT = 1000  # Don't search through more than 1000 members

# Error Handling
MAX_ERROR_MESSAGE_LENGTH = 1000
MAX_TRACEBACK_LENGTH = 2000

# Validation Patterns
DISCORD_MESSAGE_LINK_PATTERN = (
    r'https://(?:ptb\.|canary\.)?discord(?:app)?\.com/channels/'
    r'(?P<guild_id>[0-9]{15,21}|@me)/'
    r'(?P<channel_id>[0-9]{15,21})/'
    r'(?P<message_id>[0-9]{15,21})'
)

CUSTOM_EMOJI_PATTERN = r'<a?:[a-zA-Z0-9_]+:[0-9]{15,21}>'

# Premium Role Names (case insensitive)
PREMIUM_ROLE_NAMES = ["premium", "vip", "supporter", "donator"]

# Dangerous Permissions (roles with these should be restricted)
DANGEROUS_PERMISSIONS = [
    "administrator",
    "manage_guild",
    "manage_roles",
    "manage_channels",
    "manage_messages",
    "ban_members",
    "kick_members",
    "moderate_members",
    "manage_webhooks",
    "manage_emojis_and_stickers"
]

# Bot Status
BOT_STATUS_MESSAGES = [
    "Watching over the server",
    "Moderating with style",
    "Keeping things clean",
    "Ready to help!"
]

# Embed Colors (hex values)
class EmbedColors:
    SUCCESS = 0xa4ec7c
    ERROR = 0xfc6464
    WARNING = 0xfbd03b
    INFO = 0x3a3a40
    COOLDOWN = 0xa5eb78
    PREMIUM = 0xf1c40f
    MODERATION = 0xe74c3c

# Button Styles Mapping
BUTTON_STYLE_MAP = {
    'blue': 'primary',
    'gray': 'secondary',
    'grey': 'secondary',
    'green': 'success',
    'red': 'danger'
}

# Time Unit Mappings for Duration Parsing
TIME_UNITS = {
    's': SECONDS_PER_MINUTE // 60,
    'sec': SECONDS_PER_MINUTE // 60,
    'second': SECONDS_PER_MINUTE // 60,
    'seconds': SECONDS_PER_MINUTE // 60,
    'm': SECONDS_PER_MINUTE,
    'min': SECONDS_PER_MINUTE,
    'minute': SECONDS_PER_MINUTE,
    'minutes': SECONDS_PER_MINUTE,
    'h': SECONDS_PER_HOUR,
    'hr': SECONDS_PER_HOUR,
    'hour': SECONDS_PER_HOUR,
    'hours': SECONDS_PER_HOUR,
    'd': SECONDS_PER_DAY,
    'day': SECONDS_PER_DAY,
    'days': SECONDS_PER_DAY,
    'w': SECONDS_PER_WEEK,
    'week': SECONDS_PER_WEEK,
    'weeks': SECONDS_PER_WEEK
}

# Help Command Configuration
HELP_EMBED_COLOR = EmbedColors.INFO
HELP_PAGINATION_TIMEOUT = 300  # 5 minutes
MAX_COMMANDS_PER_PAGE = 10

# Logging Configuration
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
LOG_DATE_FORMAT = '%Y-%m-%d %H:%M:%S'
MAX_LOG_FILE_SIZE_MB = 50
LOG_BACKUP_COUNT = 5

# Development and Debug
DEBUG_MODE = False
VERBOSE_LOGGING = False
PERFORMANCE_MONITORING = True
