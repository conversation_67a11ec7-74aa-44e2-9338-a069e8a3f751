"""
Global error handling system for the Discord bot.
Provides centralized error handling for commands and events.
"""

import discord
from discord.ext import commands
import traceback
from typing import Optional, Union
from utils.logger import command_error, error, warning, security_warning
from embeds.prebuilt import embeds
from cogs.owner.trace import error_tracker, handle_command_error

class GlobalErrorHandler:
    """Centralized error handling for the bot"""
    
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        
    async def handle_command_error(self, ctx: commands.Context, error: commands.CommandError):
        """
        Handle command errors globally.
        
        Args:
            ctx: The command context
            error: The error that occurred
        """
        # Get the original exception if it's wrapped
        original_error = getattr(error, 'original', error)
        
        # Log the error
        command_name = ctx.command.qualified_name if ctx.command else "unknown"
        command_error(
            command_name,
            ctx.author.id,
            ctx.guild.id if ctx.guild else None,
            original_error
        )
        
        # Handle specific error types
        if isinstance(error, commands.CommandNotFound):
            # Silently ignore command not found errors
            return
            
        elif isinstance(error, commands.DisabledCommand):
            await embeds.deny(ctx, "This command is currently disabled.")
            
        elif isinstance(error, commands.NoPrivateMessage):
            await embeds.deny(ctx, "This command cannot be used in private messages.")
            
        elif isinstance(error, commands.PrivateMessageOnly):
            await embeds.deny(ctx, "This command can only be used in private messages.")
            
        elif isinstance(error, commands.CheckFailure):
            # Permission errors are handled by the permission system
            return
            
        elif isinstance(error, commands.UserInputError):
            await self._handle_user_input_error(ctx, error)
            
        elif isinstance(error, commands.CommandOnCooldown):
            await self._handle_cooldown_error(ctx, error)
            
        elif isinstance(error, commands.MaxConcurrencyReached):
            await embeds.deny(ctx, "This command is already running. Please wait for it to finish.")
            
        elif isinstance(error, discord.Forbidden):
            await embeds.deny(ctx, "I don't have permission to perform this action.")
            
        elif isinstance(error, discord.NotFound):
            await embeds.deny(ctx, "The requested resource was not found.")
            
        elif isinstance(error, discord.HTTPException):
            if error.status == 429:  # Rate limited
                await embeds.deny(ctx, "I'm being rate limited. Please try again later.")
            else:
                await embeds.deny(ctx, f"A Discord API error occurred: {error}")
                
        else:
            # Unhandled error - use the trace system
            await handle_command_error(ctx, original_error, command_name)
    
    async def _handle_user_input_error(self, ctx: commands.Context, error: commands.UserInputError):
        """Handle user input errors with helpful messages"""
        if isinstance(error, commands.MissingRequiredArgument):
            # Show command help
            help_cog = self.bot.get_cog('Help')
            if help_cog and ctx.command:
                await help_cog.show_command_help(ctx, ctx.command)
            else:
                await embeds.deny(ctx, f"Missing required argument: `{error.param.name}`")
                
        elif isinstance(error, commands.BadArgument):
            await embeds.deny(ctx, f"Invalid argument: {error}")
            
        elif isinstance(error, commands.TooManyArguments):
            await embeds.deny(ctx, "Too many arguments provided.")
            
        elif isinstance(error, commands.BadUnionArgument):
            await embeds.deny(ctx, f"Could not convert argument: {error}")
            
        elif isinstance(error, commands.ArgumentParsingError):
            await embeds.deny(ctx, f"Error parsing arguments: {error}")
            
        else:
            await embeds.deny(ctx, f"Input error: {error}")
    
    async def _handle_cooldown_error(self, ctx: commands.Context, error: commands.CommandOnCooldown):
        """Handle cooldown errors with formatted time remaining"""
        retry_after = error.retry_after
        
        if retry_after < 60:
            time_str = f"{retry_after:.1f} seconds"
        elif retry_after < 3600:
            time_str = f"{retry_after/60:.1f} minutes"
        else:
            time_str = f"{retry_after/3600:.1f} hours"
        
        await embeds.deny(ctx, f"Command is on cooldown. Try again in {time_str}.")
    
    async def handle_event_error(self, event_name: str, *args, **kwargs):
        """
        Handle errors in event listeners.
        
        Args:
            event_name: Name of the event that errored
            *args: Event arguments
            **kwargs: Event keyword arguments
        """
        exc_info = kwargs.get('exc_info')
        if exc_info:
            exc_type, exc_value, exc_traceback = exc_info
            error(f"Error in event {event_name}", exc_value)
            
            # Log additional context for specific events
            if event_name == 'on_message' and args:
                message = args[0]
                if isinstance(message, discord.Message):
                    error(f"Message error context: Guild {message.guild.id if message.guild else 'DM'}, "
                          f"Channel {message.channel.id}, User {message.author.id}")
            
            elif event_name == 'on_interaction' and args:
                interaction = args[0]
                if isinstance(interaction, discord.Interaction):
                    error(f"Interaction error context: Guild {interaction.guild_id}, "
                          f"User {interaction.user.id}, Type {interaction.type}")
    
    async def handle_security_event(self, event_type: str, user_id: int, guild_id: Optional[int], details: str):
        """
        Handle security-related events.
        
        Args:
            event_type: Type of security event
            user_id: ID of the user involved
            guild_id: ID of the guild (if applicable)
            details: Additional details about the event
        """
        security_warning(f"{event_type}: {details}", user_id, guild_id)
        
        # Additional actions based on event type
        if event_type == "SUSPICIOUS_ACTIVITY":
            # Could implement automatic actions here
            pass
        elif event_type == "RATE_LIMIT_EXCEEDED":
            # Could implement temporary restrictions
            pass
        elif event_type == "PERMISSION_ESCALATION_ATTEMPT":
            # Could implement alerts to administrators
            pass

def setup_global_error_handler(bot: commands.Bot) -> GlobalErrorHandler:
    """
    Set up the global error handler for the bot.
    
    Args:
        bot: The Discord bot instance
        
    Returns:
        GlobalErrorHandler: The configured error handler
    """
    error_handler = GlobalErrorHandler(bot)
    
    # Set the global error handler
    @bot.event
    async def on_command_error(ctx: commands.Context, error: commands.CommandError):
        await error_handler.handle_command_error(ctx, error)
    
    # Set up event error handling
    original_dispatch = bot.dispatch
    
    def dispatch_with_error_handling(event_name: str, *args, **kwargs):
        try:
            return original_dispatch(event_name, *args, **kwargs)
        except Exception as e:
            # Create a task to handle the error asynchronously
            bot.loop.create_task(
                error_handler.handle_event_error(
                    event_name, 
                    *args, 
                    exc_info=(type(e), e, e.__traceback__),
                    **kwargs
                )
            )
            # Re-raise the exception so it's still handled normally
            raise
    
    bot.dispatch = dispatch_with_error_handling
    
    return error_handler

# Utility functions for easy error reporting
async def report_user_error(ctx: commands.Context, message: str):
    """Report a user-friendly error message"""
    await embeds.deny(ctx, message)

async def report_permission_error(ctx: commands.Context, required_permission: str):
    """Report a permission error"""
    await embeds.deny(ctx, f"You need the `{required_permission}` permission to use this command.")

async def report_hierarchy_error(ctx: commands.Context, target_name: str):
    """Report a role hierarchy error"""
    await embeds.deny(ctx, f"You cannot moderate {target_name} due to role hierarchy.")

async def report_bot_permission_error(ctx: commands.Context, required_permission: str):
    """Report a bot permission error"""
    await embeds.deny(ctx, f"I need the `{required_permission}` permission to perform this action.")

async def report_validation_error(ctx: commands.Context, field_name: str, error_message: str):
    """Report a validation error"""
    await embeds.deny(ctx, f"Invalid {field_name}: {error_message}")

async def report_rate_limit_error(ctx: commands.Context):
    """Report a rate limit error"""
    await embeds.deny(ctx, "I'm being rate limited by Discord. Please try again later.")

async def report_timeout_error(ctx: commands.Context, operation: str):
    """Report a timeout error"""
    await embeds.deny(ctx, f"The {operation} operation timed out. Please try again.")
