import discord
from discord.ext import commands
from typing import Optional, Union, List
import os
import re
from collections import defaultdict
import time

# Configuration from environment variables
DEV_SERVER_ID = os.getenv('DEV_SERVER_ID')
DEV_ROLE_ID = os.getenv('DEV_ROLE_ID')
PREMIUM_ROLE_ID = os.getenv('PREMIUM_ROLE_ID')
OWNER_ID = os.getenv('OWNER_ID')

PREMIUM_ROLE_NAMES = ["premium", "vip", "supporter", "donator"]

# Permission levels configuration
PERMISSION_LEVELS = {
    'premium': {
        'role_id': PREMIUM_ROLE_ID,
        'name': 'Premium',
        'message': 'You need to buy **premium/boost** server for this feature!'
    },
    'dev': {
        'role_id': DEV_ROLE_ID,
        'user_id': OWNER_ID,
        'name': 'Developer',
        'message': 'This command is restricted to **developers** only!'
    }
}

# Cooldown tracking
cooldowns = defaultdict(dict)

def IsGlobalDev(user_id: int) -> bool:
    """Check if user is the global dev (owner)"""
    return str(user_id) == OWNER_ID

async def HasPermissionLevel(ctx: commands.Context, level: str) -> bool:
    """Check if user has the specified permission level"""
    user_id = ctx.author.id

    # Owner bypasses all checks
    if IsGlobalDev(user_id):
        return True

    if level == 'dev':
        # Check if user is the dev user
        if str(user_id) == PERMISSION_LEVELS['dev'].get('user_id'):
            return True

        # Check if user has dev role in dev server
        if DEV_SERVER_ID and DEV_ROLE_ID:
            try:
                dev_server = ctx.bot.get_guild(int(DEV_SERVER_ID))
                if dev_server:
                    member = dev_server.get_member(user_id)
                    if member and any(role.id == int(DEV_ROLE_ID) for role in member.roles):
                        return True
            except:
                pass

    elif level == 'premium':
        # Check if user has premium role in dev server
        if DEV_SERVER_ID and PREMIUM_ROLE_ID:
            try:
                dev_server = ctx.bot.get_guild(int(DEV_SERVER_ID))
                if dev_server:
                    member = dev_server.get_member(user_id)
                    if member and any(role.id == int(PREMIUM_ROLE_ID) for role in member.roles):
                        return True
            except:
                pass

        # Also check for premium role names in current server
        for role in ctx.author.roles:
            if role.name.lower() in PREMIUM_ROLE_NAMES:
                return True

    return False

async def CheckPermissionLevel(ctx: commands.Context, level: str) -> bool:
    """Check permission level and send error message if failed"""
    has_permission = await HasPermissionLevel(ctx, level)

    if not has_permission:
        from embeds.prebuilt import embeds
        message = PERMISSION_LEVELS.get(level, {}).get('message', 'You do not have permission to use this command!')
        await embeds.warn(ctx, message)
        return False

    return True

# Hierarchy checks
def CheckUserHierarchy(ctx: commands.Context, target_member: discord.Member, send_error: bool = True) -> bool:
    """Check if user can moderate target based on role hierarchy"""
    # Global dev bypasses all hierarchy checks
    if IsGlobalDev(ctx.author.id):
        return True

    if target_member.top_role.position >= ctx.author.top_role.position:
        if send_error:
            from embeds.prebuilt import embeds
            embeds.warn(ctx, f"**{target_member.display_name}** is above your role!")
        return False
    return True

def CheckBotHierarchy(ctx: commands.Context, target, send_error: bool = True) -> bool:
    """Check if bot can moderate target based on role hierarchy"""
    target_position = target.top_role.position if hasattr(target, 'top_role') else target.position
    target_name = target.display_name if hasattr(target, 'display_name') else target.name

    if target_position >= ctx.guild.me.top_role.position:
        if send_error:
            from embeds.prebuilt import embeds
            embeds.warn(ctx, f"**{target_name}** is above my role!")
        return False
    return True

def CheckUserRoleHierarchy(ctx: commands.Context, role: discord.Role, enabled: bool = True) -> bool:
    """Check if user can manage role based on hierarchy"""
    if not enabled:
        return True

    # Global dev bypasses all hierarchy checks
    if IsGlobalDev(ctx.author.id):
        return True

    if role.position >= ctx.author.top_role.position:
        from embeds.prebuilt import embeds
        embeds.warn(ctx, f"**{role.name}** is above your role!")
        return False
    return True

def CheckBotRoleHierarchy(ctx: commands.Context, role: discord.Role, enabled: bool = True) -> bool:
    """Check if bot can manage role based on hierarchy"""
    if not enabled:
        return True

    if role.position >= ctx.guild.me.top_role.position:
        from embeds.prebuilt import embeds
        embeds.warn(ctx, f"**{role.name}** is above my role!")
        return False
    return True

# Action checks
def CheckSelfAction(ctx: commands.Context, target_user: discord.User, action: str) -> bool:
    """Check if user is trying to perform action on themselves"""
    # Global dev can target themselves (for testing)
    if IsGlobalDev(ctx.author.id):
        return True

    if target_user.id == ctx.author.id:
        from embeds.prebuilt import embeds
        embeds.warn(ctx, f"You cannot {action} yourself!")
        return False
    return True

def CheckBotAction(ctx: commands.Context, target_user: discord.User) -> bool:
    """Check if user is trying to perform action on the bot"""
    if target_user.id == ctx.guild.me.id:
        from embeds.prebuilt import embeds
        embeds.warn(ctx, 'Leave me alone!')
        return False
    return True

def CheckDiscordActionable(ctx: commands.Context, member: discord.Member, action: str) -> bool:
    """Check if Discord allows the action on the member"""
    actionable = False

    action_lower = action.lower()
    if action_lower == 'kick':
        actionable = member.kickable
    elif action_lower == 'ban':
        actionable = member.bannable
    elif action_lower in ['timeout', 'mute']:
        actionable = member.moderatable
    elif action_lower in ['manage roles', 'jail']:
        actionable = member.manageable
    else:
        actionable = True

    if not actionable:
        from embeds.prebuilt import embeds
        embeds.warn(ctx, f"**{member.display_name}** is above my role!")
        return False
    return True

# Discord permission checks
def HasDiscordPermission(ctx: commands.Context, permission: str, permission_name: str, custom_message: str = None) -> bool:
    """Check if user has Discord permission"""
    # Global dev bypasses all Discord permission checks
    if IsGlobalDev(ctx.author.id):
        return True

    if not ctx.author.guild_permissions.has(permission):
        from embeds.prebuilt import embeds
        error_message = custom_message or f"You need **{permission_name}** permission to use this command!"
        embeds.warn(ctx, error_message)
        return False
    return True

def HasMultipleDiscordPermissions(ctx: commands.Context, permissions: List, permission_names: List[str]) -> bool:
    """Check if user has multiple Discord permissions"""
    # Global dev bypasses all Discord permission checks
    if IsGlobalDev(ctx.author.id):
        return True

    missing_permissions = []

    for i, permission in enumerate(permissions):
        if not ctx.author.guild_permissions.has(permission):
            missing_permissions.append(permission_names[i])

    if missing_permissions:
        from embeds.prebuilt import embeds
        if len(missing_permissions) == 1:
            permission_text = f"**{missing_permissions[0]}** permission"
        else:
            permission_text = f"**{' and '.join(missing_permissions)}** permissions"
        embeds.warn(ctx, f"You need {permission_text} to use this command!")
        return False
    return True

# Cooldown handling
def HandleCooldown(ctx: commands.Context, command_name: str, cooldown_seconds: int = 3) -> Optional[str]:
    """Handle command cooldowns"""
    if not cooldowns.get(command_name):
        cooldowns[command_name] = {}

    now = time.time()
    user_cooldowns = cooldowns[command_name]

    if ctx.author.id in user_cooldowns:
        expiration_time = user_cooldowns[ctx.author.id] + cooldown_seconds

        if now < expiration_time:
            time_left = expiration_time - now
            if time_left >= 60:
                time_string = f"{int(time_left // 60)}m {int(time_left % 60)}s"
            else:
                time_string = f"{time_left:.1f}s"
            return f"Please wait **{time_string}** before using `{command_name}` again."

    user_cooldowns[ctx.author.id] = now
    return None

# Decorator classes for easy use
class PermissionCheck:
    @staticmethod
    def owner_only():
        async def predicate(ctx):
            return await ctx.bot.is_owner(ctx.author)
        return commands.check(predicate)

    @staticmethod
    def discord_permission(permission_name: str):
        def decorator(func):
            async def wrapper(self, ctx, *args, **kwargs):
                # Convert permission name to Discord permission
                permission_map = {
                    "Administrator": discord.Permissions.administrator,
                    "ManageGuild": discord.Permissions.manage_guild,
                    "ManageRoles": discord.Permissions.manage_roles,
                    "ManageChannels": discord.Permissions.manage_channels,
                    "ManageMessages": discord.Permissions.manage_messages,
                    "BanMembers": discord.Permissions.ban_members,
                    "KickMembers": discord.Permissions.kick_members,
                    "ModerateMembers": discord.Permissions.moderate_members,
                    "ManageNicknames": discord.Permissions.manage_nicknames,
                }

                permission = permission_map.get(permission_name)
                if permission and not HasDiscordPermission(ctx, permission, permission_name):
                    return
                return await func(self, ctx, *args, **kwargs)
            return wrapper
        return decorator

    @staticmethod
    def premium():
        def decorator(func):
            async def wrapper(self, ctx, *args, **kwargs):
                if not await CheckPermissionLevel(ctx, 'premium'):
                    return
                return await func(self, ctx, *args, **kwargs)
            return wrapper
        return decorator

    @staticmethod
    def dev():
        def decorator(func):
            async def wrapper(self, ctx, *args, **kwargs):
                if not await HasPermissionLevel(ctx, 'dev'):
                    return  # Silently ignore for dev permissions
                return await func(self, ctx, *args, **kwargs)
            return wrapper
        return decorator