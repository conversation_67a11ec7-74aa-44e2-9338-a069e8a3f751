import discord
from discord.ext import commands
from typing import Optional, List, Union
import os
import re

PREMIUM_ROLE_NAMES = ["premium", "vip", "supporter", "donator"]
DEV_SERVER_ID = os.getenv('DEV_SERVER_ID')
PERMISSION_LEVELS = {
    'dev': {'role_id': os.getenv('DEV_ROLE_ID')},
    'premium': {'role_id': os.getenv('PREMIUM_ROLE_ID')}
}

async def IsBotOwner(ctx: commands.Context) -> bool:
    return await ctx.bot.is_owner(ctx.author)

def HasPremium(member: discord.Member) -> bool:
    if not member.guild:
        return False

    premium_roles = [role for role in member.roles
                    if role.name.lower() in PREMIUM_ROLE_NAMES]
    return len(premium_roles) > 0

async def HasDevRole(user_id: int, client) -> bool:
    try:
        if not DEV_SERVER_ID or not PERMISSION_LEVELS['dev']['role_id']:
            return False

        dev_server = client.get_guild(int(DEV_SERVER_ID))
        if not dev_server:
            return False

        member = await dev_server.fetch_member(user_id)
        if not member:
            return False

        dev_role = discord.utils.get(member.roles, id=int(PERMISSION_LEVELS['dev']['role_id']))
        return dev_role is not None
    except:
        return False

async def HasPremiumRole(user_id: int, client) -> bool:
    try:
        if not DEV_SERVER_ID or not PERMISSION_LEVELS['premium']['role_id']:
            return False

        dev_server = client.get_guild(int(DEV_SERVER_ID))
        if not dev_server:
            return False

        member = await dev_server.fetch_member(user_id)
        if not member:
            return False

        premium_role = discord.utils.get(member.roles, id=int(PERMISSION_LEVELS['premium']['role_id']))
        return premium_role is not None
    except:
        return False

def GetHighestRole(member: discord.Member) -> Optional[discord.Role]:
    if not member.roles:
        return None
    return max(member.roles, key=lambda r: r.position)

def CanModerate(moderator: discord.Member, target: discord.Member) -> bool:
    if moderator.guild != target.guild:
        return False

    if moderator.guild.owner == moderator:
        return True

    if moderator.guild.owner == target:
        return False

    mod_highest = GetHighestRole(moderator)
    target_highest = GetHighestRole(target)

    if not mod_highest or not target_highest:
        return False

    return mod_highest.position > target_highest.position

def HasDiscordPermission(member: discord.Member, permission: str) -> bool:
    if not member.guild:
        return False

    if member.guild.owner == member:
        return True

    # Convert PascalCase to snake_case for Discord.py
    snake_case_permission = ''.join(['_' + c.lower() if c.isupper() and i > 0 else c.lower() for i, c in enumerate(permission)])

    return getattr(member.guild_permissions, snake_case_permission, False)

async def CheckBotHierarchy(ctx: commands.Context, target: Union[discord.Member, discord.Role], send_error: bool = True) -> bool:
    if isinstance(target, discord.Member):
        target_position = target.top_role.position
        target_name = target.display_name
    else:
        target_position = target.position
        target_name = target.name

    bot_position = ctx.guild.me.top_role.position

    if target_position >= bot_position:
        if send_error:
            from embeds.embeds import embeds
            await embeds.deny(ctx, f"**{target_name}** is above my role!")
        return False
    return True

async def CheckUserRoleHierarchy(ctx: commands.Context, role: discord.Role, enabled: bool = True) -> bool:
    if not enabled:
        return True

    if role.position >= ctx.author.top_role.position:
        from embeds.embeds import embeds
        await embeds.deny(ctx, f"**{role.name}** is above your role!")
        return False
    return True

async def CheckBotRoleHierarchy(ctx: commands.Context, role: discord.Role, enabled: bool = True) -> bool:
    if not enabled:
        return True

    if role.position >= ctx.guild.me.top_role.position:
        from embeds.embeds import embeds
        await embeds.deny(ctx, f"**{role.name}** is above my role!")
        return False
    return True

class PermissionCheck:
    @staticmethod
    def owner_only():
        async def predicate(ctx):
            return await IsBotOwner(ctx)
        return commands.check(predicate)

    @staticmethod
    def premium_only():
        async def predicate(ctx):
            if await IsBotOwner(ctx):
                return True
            return HasPremium(ctx.author)
        return commands.check(predicate)

    @staticmethod
    def discord_permission(permission: str):
        async def predicate(ctx):
            if await IsBotOwner(ctx):
                return True
            return HasDiscordPermission(ctx.author, permission)
        return commands.check(predicate)

    @staticmethod
    def can_moderate_target():
        async def predicate(ctx):
            if await IsBotOwner(ctx):
                return True

            if not ctx.args or len(ctx.args) < 2:
                return True

            target = ctx.args[1]
            if isinstance(target, discord.Member):
                return CanModerate(ctx.author, target)

            return True
        return commands.check(predicate)

async def FindUser(ctx: commands.Context, user_input: str = None) -> Optional[discord.Member]:
    if not user_input:
        return ctx.author

    user_input = user_input.strip()

    # Try ID (mention or raw ID)
    user_id_match = re.search(r'(\d{15,21})', user_input)
    if user_id_match:
        user_id = int(user_id_match.group(1))
        member = ctx.guild.get_member(user_id)
        if member:
            return member

    # Try username (case insensitive)
    for member in ctx.guild.members:
        if member.name.lower() == user_input.lower():
            return member

    # Try display name (case insensitive)
    for member in ctx.guild.members:
        if member.display_name.lower() == user_input.lower():
            return member

    # Try partial username match
    for member in ctx.guild.members:
        if user_input.lower() in member.name.lower():
            return member

    # Try partial display name match
    for member in ctx.guild.members:
        if user_input.lower() in member.display_name.lower():
            return member

    return None

async def FindRole(ctx: commands.Context, role_input: str) -> Optional[discord.Role]:
    if not role_input:
        return None

    role_input = role_input.strip()

    # Try ID (mention or raw ID)
    role_id_match = re.search(r'(\d{15,21})', role_input)
    if role_id_match:
        role_id = int(role_id_match.group(1))
        role = ctx.guild.get_role(role_id)
        if role:
            return role

    # Try exact name match (case insensitive)
    for role in ctx.guild.roles:
        if role.name.lower() == role_input.lower():
            return role

    # Try partial name match
    for role in ctx.guild.roles:
        if role_input.lower() in role.name.lower():
            return role

    return None

async def FindChannel(ctx: commands.Context, channel_input: str) -> Optional[discord.TextChannel]:
    if not channel_input:
        return None

    channel_input = channel_input.strip()

    # Try ID (mention or raw ID)
    channel_id_match = re.search(r'(\d{15,21})', channel_input)
    if channel_id_match:
        channel_id = int(channel_id_match.group(1))
        channel = ctx.guild.get_channel(channel_id)
        if isinstance(channel, discord.TextChannel):
            return channel

    # Try exact name match (case insensitive)
    for channel in ctx.guild.text_channels:
        if channel.name.lower() == channel_input.lower():
            return channel

    # Try partial name match
    for channel in ctx.guild.text_channels:
        if channel_input.lower() in channel.name.lower():
            return channel

    return None

async def PermissionErrorHandler(ctx: commands.Context, error_type: str, additional_info: str = None):
    from embeds.prebuilt import embeds

    if error_type == "role_security":
        await embeds.deny(ctx, f"**Security Error:** {additional_info}")
        return

    error_messages = {
        "owner": "This command is owner-only",
        "premium": "This command requires premium role",
        "permission": "You lack the required Discord permissions",
        "hierarchy": "You cannot moderate this user (role hierarchy)"
    }

    message = error_messages.get(error_type, "Permission denied")
    await embeds.deny(ctx, message)

# Common error handlers for message parsing
async def HandleMessageLinkError(ctx, error_type: str, additional_info: str = None):
    """Handle common message link parsing errors"""

    error_messages = {
        "invalid_format": "Invalid message link format.",
        "wrong_server": "Message must be from this server.",
        "channel_not_found": "Channel not found.",
        "message_not_found": "Message not found.",
        "no_permission": "I don't have permission to access that message."
    }

    message = error_messages.get(error_type, "Invalid message link.")
    if additional_info:
        message = f"{message} {additional_info}"

    await embeds.deny(ctx, message)

async def ParseMessageLink(ctx, message_link: str):
    """Parse Discord message link and return message, channel"""
    import re

    # Discord message link pattern
    pattern = r'https://discord\.com/channels/(\d+)/(\d+)/(\d+)'
    match = re.match(pattern, message_link)

    if not match:
        await HandleMessageLinkError(ctx, "invalid_format")
        return None, None

    try:
        guild_id, channel_id, message_id = map(int, match.groups())

        # Check if message is from this server
        if guild_id != ctx.guild.id:
            await HandleMessageLinkError(ctx, "wrong_server")
            return None, None

        # Get channel
        channel = ctx.bot.get_channel(channel_id)
        if not channel:
            await HandleMessageLinkError(ctx, "channel_not_found")
            return None, None

        # Get message
        try:
            message = await channel.fetch_message(message_id)
            return message, channel
        except discord.NotFound:
            await HandleMessageLinkError(ctx, "message_not_found")
            return None, None
        except discord.Forbidden:
            await HandleMessageLinkError(ctx, "no_permission")
            return None, None

    except (ValueError, IndexError):
        await HandleMessageLinkError(ctx, "invalid_format")
        return None, None
