"""
Security utilities and audit system for the Discord bot.
Provides security checks, rate limiting, and audit logging.
"""

import discord
from discord.ext import commands
import time
from typing import Dict, Set, Optional, Tuple
from collections import defaultdict, deque
from utils.constants import DANGEROUS_PERMISSIONS
from utils.logger import security_warning, warning, error
from utils.validators import ValidationError

class SecurityAudit:
    """Security audit and monitoring system"""
    
    def __init__(self):
        # Rate limiting tracking
        self._command_usage: Dict[int, deque] = defaultdict(lambda: deque(maxlen=10))
        self._failed_attempts: Dict[int, int] = defaultdict(int)
        self._suspicious_users: Set[int] = set()
        
        # Permission escalation tracking
        self._permission_attempts: Dict[int, deque] = defaultdict(lambda: deque(maxlen=5))
        
        # Input validation tracking
        self._validation_failures: Dict[int, int] = defaultdict(int)
    
    def check_rate_limit(self, user_id: int, command_name: str, max_per_minute: int = 30) -> bool:
        """
        Check if user is rate limited for commands.
        
        Args:
            user_id: Discord user ID
            command_name: Name of the command
            max_per_minute: Maximum commands per minute
            
        Returns:
            bool: True if allowed, False if rate limited
        """
        now = time.time()
        user_commands = self._command_usage[user_id]
        
        # Remove commands older than 1 minute
        while user_commands and user_commands[0] < now - 60:
            user_commands.popleft()
        
        # Check if over limit
        if len(user_commands) >= max_per_minute:
            self._failed_attempts[user_id] += 1
            
            # Mark as suspicious after multiple rate limit hits
            if self._failed_attempts[user_id] > 5:
                self._suspicious_users.add(user_id)
                security_warning(
                    f"User marked suspicious due to repeated rate limiting",
                    user_id=user_id
                )
            
            return False
        
        # Add current command
        user_commands.append(now)
        return True
    
    def check_permission_escalation(self, user_id: int, attempted_permission: str, guild_id: int) -> bool:
        """
        Check for permission escalation attempts.
        
        Args:
            user_id: Discord user ID
            attempted_permission: Permission being attempted
            guild_id: Guild ID
            
        Returns:
            bool: True if allowed, False if suspicious
        """
        if attempted_permission in DANGEROUS_PERMISSIONS:
            now = time.time()
            attempts = self._permission_attempts[user_id]
            
            # Remove attempts older than 5 minutes
            while attempts and attempts[0] < now - 300:
                attempts.popleft()
            
            attempts.append(now)
            
            # Multiple dangerous permission attempts in short time
            if len(attempts) >= 3:
                security_warning(
                    f"Multiple dangerous permission attempts: {attempted_permission}",
                    user_id=user_id,
                    guild_id=guild_id
                )
                return False
        
        return True
    
    def log_validation_failure(self, user_id: int, validation_type: str, input_value: str, guild_id: Optional[int] = None):
        """
        Log input validation failures for security monitoring.
        
        Args:
            user_id: Discord user ID
            validation_type: Type of validation that failed
            input_value: The input that failed validation (truncated for security)
            guild_id: Guild ID if applicable
        """
        self._validation_failures[user_id] += 1
        
        # Truncate potentially malicious input for logging
        safe_input = input_value[:100] if input_value else "None"
        
        # Log the failure
        security_warning(
            f"Input validation failure ({validation_type}): {safe_input}",
            user_id=user_id,
            guild_id=guild_id
        )
        
        # Mark as suspicious after many validation failures
        if self._validation_failures[user_id] > 10:
            self._suspicious_users.add(user_id)
            security_warning(
                f"User marked suspicious due to repeated validation failures",
                user_id=user_id,
                guild_id=guild_id
            )
    
    def is_suspicious_user(self, user_id: int) -> bool:
        """Check if user is marked as suspicious"""
        return user_id in self._suspicious_users
    
    def clear_user_flags(self, user_id: int):
        """Clear security flags for a user (admin function)"""
        self._suspicious_users.discard(user_id)
        self._failed_attempts.pop(user_id, None)
        self._validation_failures.pop(user_id, None)
        self._command_usage.pop(user_id, None)
        self._permission_attempts.pop(user_id, None)
    
    def get_user_stats(self, user_id: int) -> Dict:
        """Get security stats for a user"""
        return {
            'suspicious': user_id in self._suspicious_users,
            'failed_attempts': self._failed_attempts.get(user_id, 0),
            'validation_failures': self._validation_failures.get(user_id, 0),
            'recent_commands': len(self._command_usage.get(user_id, [])),
            'permission_attempts': len(self._permission_attempts.get(user_id, []))
        }

class RoleSecurityChecker:
    """Security checks for role-related operations"""
    
    @staticmethod
    def check_role_safety(role: discord.Role, operation: str) -> Tuple[bool, Optional[str]]:
        """
        Check if a role operation is safe.
        
        Args:
            role: The Discord role
            operation: The operation being performed
            
        Returns:
            Tuple[bool, Optional[str]]: (is_safe, error_message)
        """
        # Check if role is managed
        if role.managed:
            return False, "Cannot assign managed roles (bot roles, booster roles, etc.)"
        
        # Check for dangerous permissions
        dangerous_perms = []
        for perm_name in DANGEROUS_PERMISSIONS:
            if getattr(role.permissions, perm_name, False):
                dangerous_perms.append(perm_name)
        
        if dangerous_perms:
            return False, f"Role has dangerous permissions: {', '.join(dangerous_perms)}"
        
        # Check role position (should be done by caller with guild context)
        if role.position > 50:  # Arbitrary high position check
            security_warning(f"High position role {role.id} used in {operation}")
        
        return True, None
    
    @staticmethod
    def check_role_hierarchy(member: discord.Member, role: discord.Role, bot_member: discord.Member) -> Tuple[bool, Optional[str]]:
        """
        Check role hierarchy for safety.
        
        Args:
            member: Member performing the action
            role: Role being assigned/removed
            bot_member: Bot's member object
            
        Returns:
            Tuple[bool, Optional[str]]: (is_safe, error_message)
        """
        # Check if bot can manage the role
        if role >= bot_member.top_role:
            return False, "I cannot manage this role (role hierarchy)"
        
        # Check if user can manage the role (unless they're owner)
        if not member.guild.owner == member and role >= member.top_role:
            return False, "You cannot assign a role higher than your highest role"
        
        return True, None

class InputSanitizer:
    """Sanitize and validate user inputs"""
    
    @staticmethod
    def sanitize_text_input(text: str, max_length: int = 1000) -> str:
        """
        Sanitize text input for safety.
        
        Args:
            text: Input text
            max_length: Maximum allowed length
            
        Returns:
            str: Sanitized text
        """
        if not isinstance(text, str):
            text = str(text)
        
        # Remove null bytes and other dangerous characters
        text = text.replace('\x00', '')
        text = text.replace('\r', '')
        
        # Limit length
        if len(text) > max_length:
            text = text[:max_length]
        
        # Remove excessive whitespace
        text = ' '.join(text.split())
        
        return text
    
    @staticmethod
    def check_for_injection_attempts(text: str) -> bool:
        """
        Check for potential injection attempts.
        
        Args:
            text: Input text to check
            
        Returns:
            bool: True if suspicious patterns found
        """
        suspicious_patterns = [
            r'<script',
            r'javascript:',
            r'data:',
            r'vbscript:',
            r'onload=',
            r'onerror=',
            r'eval\(',
            r'exec\(',
            r'system\(',
            r'__import__',
            r'subprocess',
        ]
        
        text_lower = text.lower()
        for pattern in suspicious_patterns:
            if pattern in text_lower:
                return True
        
        return False

# Global security audit instance
security_audit = SecurityAudit()

# Decorator for rate limiting commands
def rate_limit(max_per_minute: int = 30):
    """Decorator to add rate limiting to commands"""
    def decorator(func):
        async def wrapper(self, ctx: commands.Context, *args, **kwargs):
            if not security_audit.check_rate_limit(ctx.author.id, ctx.command.name, max_per_minute):
                from embeds.prebuilt import embeds
                await embeds.deny(ctx, "You're using commands too quickly. Please slow down.")
                return
            
            return await func(self, ctx, *args, **kwargs)
        return wrapper
    return decorator

# Decorator for permission escalation checking
def check_permission_escalation(permission: str):
    """Decorator to check for permission escalation attempts"""
    def decorator(func):
        async def wrapper(self, ctx: commands.Context, *args, **kwargs):
            if not security_audit.check_permission_escalation(
                ctx.author.id, 
                permission, 
                ctx.guild.id if ctx.guild else None
            ):
                from embeds.prebuilt import embeds
                await embeds.deny(ctx, "Suspicious permission activity detected.")
                return
            
            return await func(self, ctx, *args, **kwargs)
        return wrapper
    return decorator
