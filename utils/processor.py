import discord
from discord.ext import commands
from typing import Optional, Dict, Any
import asyncio
from collections import defaultdict
import time

# Import permission functions (will be globally available)
from utils.permissions import (
    HasPermissionLevel, HasDiscordPermission, HasMultipleDiscordPermissions,
    CheckPermissionLevel, HandleCooldown, IsGlobalDev
)

class InvalidUsageError(Exception):
    """Custom error class for invalid command usage"""
    def __init__(self, command_name: str):
        super().__init__(f"Invalid usage for command: {command_name}")
        self.name = 'InvalidUsageError'
        self.command_name = command_name

async def ProcessCommand(message: discord.Message, is_edit: bool = False):
    """
    Process command with prefix detection and cooldown handling
    """
    if not message.client or not hasattr(message.client, 'commands'):
        return
    if not message.guild:  # Skip DMs
        return
    if message.author.bot:  # Skip bots
        return
    
    client = message.client
    
    # Check for valid prefix
    prefix_result = await CheckPrefixes(message)
    if not prefix_result:
        return
    
    prefix = prefix_result
    args = message.content[len(prefix):].strip().split()
    if not args:
        return
    
    command_name = args.pop(0).lower()
    
    # Find command
    command = client.get_command(command_name)
    if not command:
        return
    
    # Create context
    ctx = await client.get_context(message)
    if not ctx.valid:
        return
    
    try:
        # Check command permissions
        if hasattr(command, 'permission') and command.permission:
            has_permission = await CheckCommandPermission(ctx, command.permission)
            if not has_permission:
                return
        
        # Handle cooldowns (skip for edited messages)
        if not is_edit:
            cooldown_message = HandleCooldown(ctx, command.name, getattr(command, 'cooldown', 3))
            if cooldown_message:
                from embeds.prebuilt import embeds
                return await embeds.warn(ctx, cooldown_message)
        
        # Execute command
        await command.invoke(ctx)
        
    except InvalidUsageError:
        # Show help for the command
        await RunHelpCommand(ctx, command.name)
    except Exception as error:
        # Handle other errors
        from embeds.prebuilt import embeds
        await embeds.deny(ctx, 'There was an error **executing** that command, Please report this to **support server** immediately.')
        
        # Log error if error logger is available
        if hasattr(client, 'error_logger'):
            client.error_logger.log(error, f"Command Execution: {command.name}", {
                'command': command.name,
                'args': ' '.join(args) or '',
                'user': message.author,
                'guild': message.guild,
                'channel': message.channel
            })

async def CheckCommandPermission(ctx: commands.Context, permission: str) -> bool:
    """
    Check if user has permission to run command
    Dev permissions are silently ignored, others show error messages
    """
    permission_lower = permission.lower()
    
    # Map permission strings to permission checks
    if permission_lower == 'administrator':
        return HasDiscordPermission(ctx, discord.Permissions.administrator, 'Administrator')
    
    elif permission_lower == 'manage messages':
        return HasDiscordPermission(ctx, discord.Permissions.manage_messages, 'Manage Messages')
    
    elif permission_lower == 'manage guild':
        return HasDiscordPermission(ctx, discord.Permissions.manage_guild, 'Manage Guild')
    
    elif permission_lower == 'manage expressions':
        return HasDiscordPermission(ctx, discord.Permissions.manage_guild_expressions, 'Manage Expressions')
    
    elif permission_lower == 'moderate members':
        return HasDiscordPermission(ctx, discord.Permissions.moderate_members, 'Moderate Members')
    
    elif permission_lower == 'manage guild & manage roles':
        return HasMultipleDiscordPermissions(ctx,
            [discord.Permissions.manage_guild, discord.Permissions.manage_roles],
            ['Manage Guild', 'Manage Roles'])
    
    elif permission_lower == 'manage roles':
        return HasDiscordPermission(ctx, discord.Permissions.manage_roles, 'Manage Roles')
    
    elif permission_lower == 'manage channels':
        return HasDiscordPermission(ctx, discord.Permissions.manage_channels, 'Manage Channels')
    
    elif permission_lower == 'manage nicknames':
        return HasDiscordPermission(ctx, discord.Permissions.manage_nicknames, 'Manage Nicknames')
    
    elif permission_lower == 'ban members':
        return HasDiscordPermission(ctx, discord.Permissions.ban_members, 'Ban Members')
    
    elif permission_lower == 'kick members':
        return HasDiscordPermission(ctx, discord.Permissions.kick_members, 'Kick Members')
    
    elif permission_lower == 'premium':
        return await CheckPermissionLevel(ctx, 'premium')
    
    elif permission_lower == 'dev':
        # Dev permissions are silently ignored (no error message)
        return await HasPermissionLevel(ctx, 'dev')
    
    else:
        return True  # Allow by default for unknown permissions

async def CheckPrefixes(message: discord.Message) -> Optional[str]:
    """
    Check for valid prefixes in message
    Returns the prefix if found, None otherwise
    """
    # Get guild prefix from database
    guild_prefix = await GetGuildPrefix(message.guild.id)
    
    # Check guild prefix
    if message.content.startswith(guild_prefix):
        return guild_prefix
    
    # Check user self prefix
    user_prefix = await GetUserPrefix(message.author.id)
    if user_prefix and message.content.startswith(user_prefix):
        return user_prefix
    
    # Check bot mention
    bot_mention = f"<@{message.client.user.id}>"
    bot_mention_nick = f"<@!{message.client.user.id}>"
    
    if message.content.startswith(bot_mention):
        return bot_mention + " "
    elif message.content.startswith(bot_mention_nick):
        return bot_mention_nick + " "
    
    return None

async def GetGuildPrefix(guild_id: int) -> str:
    """Get the current prefix for a guild"""
    try:
        from database.database import db
        guild_data = db.guilds.get_guild(guild_id)
        if guild_data and guild_data.get('prefix'):
            return guild_data['prefix']
    except:
        pass
    return ','  # Default prefix

async def GetUserPrefix(user_id: int) -> Optional[str]:
    """Get the current self prefix for a user"""
    try:
        from database.database import db
        user_data = db.users.get_user_prefix(user_id)
        if user_data:
            return user_data.get('prefix')
    except:
        pass
    return None

async def SetGuildPrefix(guild_id: int, prefix: str) -> bool:
    """Set guild prefix"""
    try:
        from database.database import db
        return db.guilds.update_guild_prefix(guild_id, prefix)
    except:
        return False

async def SetUserPrefix(user_id: int, prefix: str) -> bool:
    """Set user self prefix"""
    try:
        from database.database import db
        return db.users.set_user_prefix(user_id, prefix)
    except:
        return False

async def ClearUserPrefix(user_id: int) -> bool:
    """Clear user self prefix"""
    try:
        from database.database import db
        return db.users.clear_user_prefix(user_id)
    except:
        return False

async def RunHelpCommand(ctx: commands.Context, command_name: str):
    """Run help command for a specific command"""
    help_command = ctx.bot.get_command('help') or ctx.bot.get_command('h')
    
    if help_command:
        try:
            await help_command.invoke(ctx, command_name)
        except:
            pass

# Global command processor setup
def SetupCommandProcessor(bot: commands.Bot):
    """Setup the command processor for the bot"""
    
    @bot.event
    async def on_message(message):
        # Process commands
        await ProcessCommand(message)
    
    @bot.event
    async def on_message_edit(before, after):
        # Process edited messages as commands
        await ProcessCommand(after, is_edit=True)
    
    print("Command processor setup complete")
