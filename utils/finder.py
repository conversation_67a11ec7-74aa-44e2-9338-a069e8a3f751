import discord
from discord.ext import commands
from typing import Optional
import re

async def FindMember(ctx: commands.Context, user_input: str = None) -> Optional[discord.Member]:
    """Find a member within the current server only"""
    if not user_input:
        return ctx.author

    user_input = user_input.strip()

    # Try ID (mention or raw ID)
    user_id_match = re.search(r'(\d{15,21})', user_input)
    if user_id_match:
        user_id = int(user_id_match.group(1))
        member = ctx.guild.get_member(user_id)
        if member:
            return member

    # Try username (case insensitive)
    for member in ctx.guild.members:
        if member.name.lower() == user_input.lower():
            return member

    # Try display name (case insensitive)
    for member in ctx.guild.members:
        if member.display_name.lower() == user_input.lower():
            return member

    return None

async def FindUser(ctx: commands.Context, user_input: str = None) -> Optional[discord.User]:
    """Find a user globally (can be outside the server)"""
    if not user_input:
        return ctx.author

    user_input = user_input.strip()

    # Try ID (mention or raw ID) - this works globally
    user_id_match = re.search(r'(\d{15,21})', user_input)
    if user_id_match:
        user_id = int(user_id_match.group(1))

        # First try to get as member (if in server)
        member = ctx.guild.get_member(user_id)
        if member:
            return member

        # Then try to fetch as user globally
        try:
            user = await ctx.bot.fetch_user(user_id)
            if user:
                return user
        except (discord.NotFound, discord.HTTPException):
            pass

    # For non-ID searches, first check server members
    member = await FindMember(ctx, user_input)
    if member:
        return member

    return None

async def FindRole(ctx: commands.Context, role_input: str) -> Optional[discord.Role]:
    if not role_input:
        return None

    role_input = role_input.strip()

    # Try ID (mention or raw ID)
    role_id_match = re.search(r'(\d{15,21})', role_input)
    if role_id_match:
        role_id = int(role_id_match.group(1))
        role = ctx.guild.get_role(role_id)
        if role:
            return role

    # Try exact name match (case insensitive)
    for role in ctx.guild.roles:
        if role.name.lower() == role_input.lower():
            return role

    return None