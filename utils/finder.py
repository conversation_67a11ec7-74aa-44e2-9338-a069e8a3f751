import discord
from discord.ext import commands
from typing import Optional, List, Dict
import re
import time

# Constants
MEMBER_SEARCH_LIMIT = 1000

async def FindMember(ctx: commands.Context, user_input: str = None) -> Optional[discord.Member]:
    """Find a member within the current server only (optimized)"""
    if not user_input:
        return ctx.author

    user_input = user_input.strip()
    start_time = time.time()

    # Try ID first (fastest lookup)
    try:
        # Extract ID from mention or use as raw ID
        import re
        id_match = re.search(r'(\d{15,21})', user_input)
        if id_match:
            user_id = int(id_match.group(1))
            member = ctx.guild.get_member(user_id)
            if member:
                return member
    except (ValueError, AttributeError):
        pass

    # For large guilds, limit search to avoid performance issues
    member_count = ctx.guild.member_count
    if member_count > MEMBER_SEARCH_LIMIT:
        # Only do exact matches for large guilds
        member_dict = {m.name.lower(): m for m in ctx.guild.members}
        display_dict = {m.display_name.lower(): m for m in ctx.guild.members}

        # Try exact username match
        if user_input.lower() in member_dict:
            return member_dict[user_input.lower()]

        # Try exact display name match
        if user_input.lower() in display_dict:
            return display_dict[user_input.lower()]

        return None

    # For smaller guilds, do full search
    user_input_lower = user_input.lower()

    # Try exact username match
    for member in ctx.guild.members:
        if member.name.lower() == user_input_lower:
            return member

    # Try exact display name match
    for member in ctx.guild.members:
        if member.display_name.lower() == user_input_lower:
            return member

    # Try partial matches (only for small guilds)
    if member_count <= 100:
        for member in ctx.guild.members:
            if user_input_lower in member.name.lower() or user_input_lower in member.display_name.lower():
                return member

    return None

async def FindUser(ctx: commands.Context, user_input: str = None) -> Optional[discord.User]:
    """Find a user globally (can be outside the server)"""
    if not user_input:
        return ctx.author

    user_input = user_input.strip()

    # Try ID (mention or raw ID) - this works globally
    user_id_match = re.search(r'(\d{15,21})', user_input)
    if user_id_match:
        user_id = int(user_id_match.group(1))

        # First try to get as member (if in server)
        member = ctx.guild.get_member(user_id)
        if member:
            return member

        # Then try to fetch as user globally
        try:
            user = await ctx.bot.fetch_user(user_id)
            if user:
                return user
        except (discord.NotFound, discord.HTTPException):
            pass

    # For non-ID searches, first check server members
    member = await FindMember(ctx, user_input)
    if member:
        return member

    return None

async def FindRole(ctx: commands.Context, role_input: str) -> Optional[discord.Role]:
    if not role_input:
        return None

    role_input = role_input.strip()

    # Try ID (mention or raw ID)
    role_id_match = re.search(r'(\d{15,21})', role_input)
    if role_id_match:
        role_id = int(role_id_match.group(1))
        role = ctx.guild.get_role(role_id)
        if role:
            return role

    # Try exact name match (case insensitive)
    for role in ctx.guild.roles:
        if role.name.lower() == role_input.lower():
            return role

    return None