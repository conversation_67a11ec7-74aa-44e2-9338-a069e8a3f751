"""
Button role event handlers for Discord bot.
Handles button interactions for role assignment/removal.
"""

import discord
from discord.ext import commands
from database.database import db
from utils.permissions import PermissionErrorHandler
from utils.validators import Validators, ValidationError
from utils.constants import DANGEROUS_PERMISSIONS
from embeds.prebuilt import embeds

class ButtonRoleEvents(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    @commands.Cog.listener()
    async def on_interaction(self, interaction: discord.Interaction):
        """Handle button interactions for role assignment"""
        # Only handle button interactions
        if interaction.type != discord.InteractionType.component:
            return
        
        # Only handle button role interactions
        if not interaction.data.get('custom_id', '').startswith('button_role_'):
            return
        
        try:
            # Extract role ID from custom_id
            custom_id = interaction.data['custom_id']
            role_id_str = custom_id.replace('button_role_', '')
            
            # Validate role ID
            try:
                role_id = Validators.validate_discord_id(role_id_str)
            except ValidationError:
                await interaction.response.send_message(
                    "❌ Invalid button configuration.", 
                    ephemeral=True
                )
                return
            
            # Get the role
            role = interaction.guild.get_role(role_id)
            if not role:
                await interaction.response.send_message(
                    "❌ This role no longer exists.", 
                    ephemeral=True
                )
                # Clean up deleted role from database
                await self.cleanup_deleted_role(role_id)
                return
            
            # Security checks
            if not await self.check_role_security(interaction, role):
                return
            
            # Check if user already has the role
            member = interaction.user
            if role in member.roles:
                # Remove role
                try:
                    await member.remove_roles(role, reason="Button role removal")
                    await interaction.response.send_message(
                        f"✅ Removed role **{role.name}**", 
                        ephemeral=True
                    )
                except discord.Forbidden:
                    await interaction.response.send_message(
                        "❌ I don't have permission to remove this role.", 
                        ephemeral=True
                    )
                except discord.HTTPException as e:
                    await interaction.response.send_message(
                        f"❌ Failed to remove role: {e}", 
                        ephemeral=True
                    )
            else:
                # Add role
                try:
                    await member.add_roles(role, reason="Button role assignment")
                    await interaction.response.send_message(
                        f"✅ Added role **{role.name}**", 
                        ephemeral=True
                    )
                except discord.Forbidden:
                    await interaction.response.send_message(
                        "❌ I don't have permission to assign this role.", 
                        ephemeral=True
                    )
                except discord.HTTPException as e:
                    await interaction.response.send_message(
                        f"❌ Failed to assign role: {e}", 
                        ephemeral=True
                    )
                    
        except Exception as e:
            print(f"Error in button role interaction: {e}")
            try:
                await interaction.response.send_message(
                    "❌ An error occurred while processing your request.", 
                    ephemeral=True
                )
            except:
                pass  # Interaction might already be responded to

    async def check_role_security(self, interaction: discord.Interaction, role: discord.Role) -> bool:
        """
        Check if the role is safe to assign via button roles.
        
        Args:
            interaction: The Discord interaction
            role: The role to check
            
        Returns:
            bool: True if role is safe to assign, False otherwise
        """
        # Check if bot can manage this role
        if role >= interaction.guild.me.top_role:
            await interaction.response.send_message(
                "❌ I cannot manage this role (role hierarchy).", 
                ephemeral=True
            )
            return False
        
        # Check if role is managed (bot roles, booster roles, etc.)
        if role.managed:
            await interaction.response.send_message(
                "❌ Cannot assign managed roles (bot roles, booster roles, etc.)", 
                ephemeral=True
            )
            return False
        
        # Check for dangerous permissions
        dangerous_perms = []
        for perm_name in DANGEROUS_PERMISSIONS:
            if getattr(role.permissions, perm_name, False):
                dangerous_perms.append(perm_name)
        
        if dangerous_perms:
            await interaction.response.send_message(
                f"❌ This role has dangerous permissions and cannot be self-assigned: {', '.join(dangerous_perms)}", 
                ephemeral=True
            )
            return False
        
        return True

    async def cleanup_deleted_role(self, role_id: int):
        """Clean up deleted role from database"""
        try:
            success = db.guilds.cleanup_deleted_role(role_id)
            if success:
                print(f"Cleaned up deleted role {role_id} from database")
            else:
                print(f"Failed to clean up deleted role {role_id}")
        except Exception as e:
            print(f"Error cleaning up deleted role {role_id}: {e}")

async def setup(bot):
    await bot.add_cog(ButtonRoleEvents(bot))
