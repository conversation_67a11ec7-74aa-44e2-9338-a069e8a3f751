import discord
from discord.ext import commands
from database.database import db

class GuildEvents(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    @commands.Cog.listener()
    async def on_guild_join(self, guild):
        """Automatically create database entry when bot joins a guild"""
        try:
            # Create guild entry in database
            success = db.guilds.create_guild(guild.id)
            if success:
                print(f"Created database entry for guild: {guild.name} ({guild.id})")
            else:
                print(f"Failed to create database entry for guild: {guild.name} ({guild.id})")
        except Exception as e:
            print(f"Error creating database entry for guild {guild.name} ({guild.id}): {e}")

    @commands.Cog.listener()
    async def on_guild_remove(self, guild):
        """Clean up when bot leaves a guild (optional)"""
        try:
            # Note: Due to foreign key constraints with CASCADE, 
            # removing guild will also remove all reaction_roles and button_roles
            print(f"Bot left guild: {guild.name} ({guild.id})")
            # We could optionally clean up the database here, but keeping data might be useful
            # if the bot rejoins the same server later
        except Exception as e:
            print(f"Error handling guild removal for {guild.name} ({guild.id}): {e}")

    @commands.Cog.listener()
    async def on_ready(self):
        """Ensure all current guilds have database entries when bot starts"""
        try:
            print("Ensuring all guilds have database entries...")
            created_count = 0
            for guild in self.bot.guilds:
                success = db.guilds.create_guild(guild.id)
                if success:
                    created_count += 1
            
            if created_count > 0:
                print(f"Created database entries for {created_count} guilds")
            else:
                print("All guilds already have database entries")
                
        except Exception as e:
            print(f"Error ensuring guild database entries: {e}")

async def setup(bot):
    await bot.add_cog(GuildEvents(bot))
