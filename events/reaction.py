import discord
from discord.ext import commands
import asyncio
from typing import Set, Tuple

# Import global utilities
from database.database import db

class ReactionRoleEvents(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        # Track ongoing role operations to prevent race conditions
        self._role_operations: Set[Tuple[int, int, int]] = set()  # (user_id, guild_id, role_id)
        self._operation_lock = asyncio.Lock()

    @commands.Cog.listener()
    async def on_raw_reaction_add(self, payload):
        """Handle reaction role assignment"""
        # Ignore bot reactions
        if payload.user_id == self.bot.user.id:
            return

        # Get reaction role data from database
        reaction_role = await self.get_reaction_role(
            payload.guild_id,
            payload.channel_id,
            payload.message_id,
            str(payload.emoji)
        )

        if not reaction_role:
            return

        guild = self.bot.get_guild(payload.guild_id)
        if not guild:
            return

        member = guild.get_member(payload.user_id)
        if not member:
            return

        role = guild.get_role(reaction_role['role_id'])
        if not role:
            # Role was deleted, should clean up from database
            await self.cleanup_deleted_role(reaction_role['role_id'])
            return

        # Prevent race conditions
        operation_key = (member.id, guild.id, role.id)
        async with self._operation_lock:
            if operation_key in self._role_operations:
                return  # Operation already in progress
            self._role_operations.add(operation_key)

        try:
            # Check if user already has the role (double-check after lock)
            if role in member.roles:
                return

            # Check role hierarchy
            if role >= guild.me.top_role:
                print(f"Cannot assign role {role.id} - higher than bot's role in guild {guild.id}")
                return

            # Check for dangerous permissions
            dangerous_perms = ['administrator', 'manage_guild', 'manage_roles', 'manage_channels', 'manage_messages', 'ban_members', 'kick_members', 'moderate_members']
            for perm_name in dangerous_perms:
                if getattr(role.permissions, perm_name, False):
                    print(f"Blocked assignment of dangerous role {role.id} with {perm_name} permission")
                    return

            await member.add_roles(role, reason="Reaction role assignment")

        except discord.Forbidden:
            print(f"Missing permission to assign role {role.id} to user {member.id} in guild {guild.id}")
        except discord.HTTPException as e:
            print(f"HTTP error assigning role {role.id} to user {member.id} in guild {guild.id}: {e}")
        finally:
            # Always remove from operations set
            async with self._operation_lock:
                self._role_operations.discard(operation_key)

    @commands.Cog.listener()
    async def on_raw_reaction_remove(self, payload):
        """Handle reaction role removal"""
        if payload.user_id == self.bot.user.id:
            return

        # Get reaction role data from database
        reaction_role = await self.get_reaction_role(
            payload.guild_id,
            payload.channel_id,
            payload.message_id,
            str(payload.emoji)
        )

        if not reaction_role:
            return

        guild = self.bot.get_guild(payload.guild_id)
        if not guild:
            return

        member = guild.get_member(payload.user_id)
        if not member:
            return

        role = guild.get_role(reaction_role['role_id'])
        if not role:
            return

        try:
            await member.remove_roles(role, reason="Reaction role removal")
        except discord.Forbidden:
            pass
        except discord.HTTPException:
            pass

    async def get_reaction_role(self, guild_id, channel_id, message_id, emoji):
        """Get reaction role from database"""
        return db.guilds.get_reaction_role(guild_id, channel_id, message_id, emoji)

    async def cleanup_deleted_role(self, role_id):
        """Clean up deleted role from database"""
        return db.guilds.cleanup_deleted_role(role_id)

async def setup(bot):
    await bot.add_cog(ReactionRoleEvents(bot))
