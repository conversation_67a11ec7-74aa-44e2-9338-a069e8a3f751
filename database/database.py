import mysql.connector
import mysql.connector.pooling
import os
import asyncio
import time
from typing import Optional, Dict, Any
from database.models.users import UserModel
from database.models.guild import GuildModel
from database.redis.cache import RedisCache
# Database configuration constants
DEFAULT_DB_POOL_SIZE = 10
DB_CONNECTION_TIMEOUT = 30

class Database:
    def __init__(self):
        self.mysql_pool = None
        self.cache = RedisCache()
        self.users = None
        self.guilds = None
        
    async def connect(self):
        try:
            # Validate required environment variables
            required_vars = ['DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME']
            missing_vars = [var for var in required_vars if not os.getenv(var)]
            if missing_vars:
                raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")

            # Connect to MySQL
            self.mysql_pool = mysql.connector.pooling.MySQLConnectionPool(
                pool_name="blur_pool",
                pool_size=int(os.getenv('DB_POOL_SIZE', DEFAULT_DB_POOL_SIZE)),
                host=os.getenv('DB_HOST'),
                port=int(os.getenv('DB_PORT', 3306)),
                user=os.getenv('DB_USER'),
                password=os.getenv('DB_PASSWORD'),
                database=os.getenv('DB_NAME'),
                charset='utf8mb4',
                collation='utf8mb4_unicode_ci',
                autocommit=True,
                connection_timeout=DB_CONNECTION_TIMEOUT,
                raise_on_warnings=True
            )
            
            # Initialize models
            self.users = UserModel(self.mysql_pool)
            self.guilds = GuildModel(self.mysql_pool)
            
            # Connect to Redis
            await self.cache.connect()
            
            # Create tables
            await self.create_tables()
            
            # Cache all data on startup
            await self.cache_all_data()
            
            print("Database connected and cached successfully")
            
        except Exception as e:
            print(f"Database connection failed: {e}")
    
    async def create_tables(self):
        await self.users.create_table()
        await self.guilds.create_table()
    
    async def cache_all_data(self):
        """Cache all guild and user data on startup with improved efficiency"""
        try:
            start_time = time.time()
            print("Starting database cache initialization")

            # Cache all users
            all_users = self.users.get_all_users()
            user_cache_data = {}
            for user in all_users:
                key = self.cache.get_cache_key("user", f"{user['user_id']}:{user['guild_id']}")
                user_cache_data[key] = user

            # Cache all guilds
            all_guilds = self.guilds.get_all_guilds()
            guild_cache_data = {}
            for guild in all_guilds:
                key = self.cache.get_cache_key("guild", str(guild['guild_id']))
                guild_cache_data[key] = guild

            # Bulk cache everything
            if user_cache_data:
                self.cache.bulk_set(user_cache_data, ttl=7200)  # 2 hours
            if guild_cache_data:
                self.cache.bulk_set(guild_cache_data, ttl=7200)  # 2 hours

            elapsed = time.time() - start_time
            print(f"Cache initialized: {len(user_cache_data)} users, {len(guild_cache_data)} guilds in {elapsed:.2f}s")

        except Exception as e:
            print(f"Database error in cache_all_data: {e}")
    
    async def get_user(self, user_id: int, guild_id: int):
        cache_key = self.cache.get_cache_key("user", f"{user_id}:{guild_id}")
        
        # Try cache first
        cached_user = self.cache.get(cache_key)
        if cached_user:
            return cached_user
        
        # Get from database
        user = self.users.get_user(user_id, guild_id)
        if user:
            self.cache.set(cache_key, user, ttl=3600)
        
        return user
    
    async def create_user(self, user_id: int, username: str, display_name: str, guild_id: int) -> bool:
        success = self.users.create_user(user_id, username, display_name, guild_id)
        if success:
            # Clear cache to force refresh
            cache_key = self.cache.get_cache_key("user", f"{user_id}:{guild_id}")
            self.cache.delete(cache_key)
        return success
    
    async def update_user_premium(self, user_id: int, guild_id: int, premium: bool) -> bool:
        success = self.users.update_user_premium(user_id, guild_id, premium)
        if success:
            cache_key = self.cache.get_cache_key("user", f"{user_id}:{guild_id}")
            self.cache.delete(cache_key)
        return success
    
    async def add_warning(self, user_id: int, guild_id: int) -> int:
        warnings = self.users.add_warning(user_id, guild_id)
        if warnings > 0:
            cache_key = self.cache.get_cache_key("user", f"{user_id}:{guild_id}")
            self.cache.delete(cache_key)
        return warnings
    
    async def get_guild(self, guild_id: int):
        cache_key = self.cache.get_cache_key("guild", str(guild_id))
        
        # Try cache first
        cached_guild = self.cache.get(cache_key)
        if cached_guild:
            return cached_guild
        
        # Get from database
        guild = self.guilds.get_guild(guild_id)
        if guild:
            self.cache.set(cache_key, guild, ttl=3600)
        
        return guild
    
    async def create_guild(self, guild_id: int, prefix: str = ',') -> bool:
        success = self.guilds.create_guild(guild_id, prefix)
        if success:
            cache_key = self.cache.get_cache_key("guild", str(guild_id))
            self.cache.delete(cache_key)
        return success
    
    async def cleanup_cache(self):
        # Redis handles TTL automatically, no manual cleanup needed
        self.cache.cleanup()
    
    def get_cache_stats(self):
        return self.cache.get_stats()

# Global database instance
db = Database()
