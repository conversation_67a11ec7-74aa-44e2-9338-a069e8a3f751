import mysql.connector
from typing import Optional, Dict, List

class GuildModel:
    def __init__(self, mysql_pool):
        self.mysql_pool = mysql_pool
    
    async def create_table(self):
        connection = self.mysql_pool.get_connection()
        cursor = connection.cursor()
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS guild_settings (
                guild_id BIGINT PRIMARY KEY,
                prefix VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT ',',
                mod_log_channel BIGINT,
                welcome_channel BIGINT,
                auto_role BIGINT,
                premium_role BIGINT,
                mute_role BIGINT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

                INDEX idx_guild_settings_created_at (created_at),
                INDEX idx_guild_settings_updated_at (updated_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS guild_modules (
                guild_id BIGINT,
                module_name VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
                enabled BOOLEAN DEFAULT TRUE,
                PRIMARY KEY (guild_id, module_name),
                FOREIGN KEY (guild_id) REFERENCES guild_settings(guild_id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)

        cursor.execute("""
            CREATE TABLE IF NOT EXISTS reaction_roles (
                id INT AUTO_INCREMENT PRIMARY KEY,
                guild_id BIGINT NOT NULL,
                channel_id BIGINT NOT NULL,
                message_id BIGINT NOT NULL,
                emoji VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                role_id BIGINT NOT NULL,
                created_by BIGINT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE KEY unique_reaction (guild_id, channel_id, message_id, emoji),
                INDEX idx_message (guild_id, channel_id, message_id),
                FOREIGN KEY (guild_id) REFERENCES guild_settings(guild_id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)

        cursor.execute("""
            CREATE TABLE IF NOT EXISTS button_roles (
                id INT AUTO_INCREMENT PRIMARY KEY,
                guild_id BIGINT NOT NULL,
                channel_id BIGINT NOT NULL,
                message_id BIGINT NOT NULL,
                role_id BIGINT NOT NULL,
                button_color VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                button_emoji VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
                button_label VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                button_index INT NOT NULL,
                created_by BIGINT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_message (guild_id, channel_id, message_id),
                FOREIGN KEY (guild_id) REFERENCES guild_settings(guild_id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        cursor.close()
        connection.close()
    
    def get_all_guilds(self) -> List[Dict]:
        connection = self.mysql_pool.get_connection()
        cursor = connection.cursor(dictionary=True)
        
        cursor.execute("SELECT * FROM guild_settings")
        guilds = cursor.fetchall()
        
        cursor.close()
        connection.close()
        
        # Convert datetime to string for JSON serialization
        for guild in guilds:
            if guild.get('created_at'):
                guild['created_at'] = guild['created_at'].isoformat()
            if guild.get('updated_at'):
                guild['updated_at'] = guild['updated_at'].isoformat()
        
        return guilds
    
    def get_guild(self, guild_id: int) -> Optional[Dict]:
        connection = None
        try:
            connection = self.mysql_pool.get_connection()
            cursor = connection.cursor(dictionary=True)

            cursor.execute(
                "SELECT * FROM guild_settings WHERE guild_id = %s",
                (guild_id,)
            )

            guild = cursor.fetchone()
            cursor.close()

            if guild:
                if guild.get('created_at'):
                    guild['created_at'] = guild['created_at'].isoformat()
                if guild.get('updated_at'):
                    guild['updated_at'] = guild['updated_at'].isoformat()

            return guild
        except Exception as e:
            print(f"Database error in get_guild: {e}")
            return None
        finally:
            if connection:
                connection.close()  # Return connection to pool
    
    def create_guild(self, guild_id: int, prefix: str = ',') -> bool:
        connection = None
        try:
            # Input validation
            if not isinstance(guild_id, int) or guild_id <= 0:
                print(f"Invalid guild_id: {guild_id}")
                return False
            if not isinstance(prefix, str) or len(prefix) > 10:
                print(f"Invalid prefix: {prefix}")
                return False

            connection = self.mysql_pool.get_connection()
            cursor = connection.cursor()

            cursor.execute("""
                INSERT INTO guild_settings (guild_id, prefix)
                VALUES (%s, %s)
                ON DUPLICATE KEY UPDATE
                updated_at = CURRENT_TIMESTAMP
            """, (guild_id, prefix))

            cursor.close()
            return True
        except Exception as e:
            print(f"Error creating guild: {e}")
            return False
        finally:
            if connection:
                connection.close()  # Return connection to pool
    
    def update_guild_prefix(self, guild_id: int, prefix: str) -> bool:
        try:
            connection = self.mysql_pool.get_connection()
            cursor = connection.cursor()
            
            cursor.execute(
                "UPDATE guild_settings SET prefix = %s WHERE guild_id = %s",
                (prefix, guild_id)
            )
            
            cursor.close()
            connection.close()
            return True
        except Exception as e:
            print(f"Error updating guild prefix: {e}")
            return False
    
    def update_guild_channel(self, guild_id: int, channel_type: str, channel_id: int) -> bool:
        try:
            connection = self.mysql_pool.get_connection()
            cursor = connection.cursor()
            
            valid_channels = ['mod_log_channel', 'welcome_channel']
            if channel_type not in valid_channels:
                return False
            
            cursor.execute(
                f"UPDATE guild_settings SET {channel_type} = %s WHERE guild_id = %s",
                (channel_id, guild_id)
            )
            
            cursor.close()
            connection.close()
            return True
        except Exception as e:
            print(f"Error updating guild channel: {e}")
            return False
    
    def update_guild_role(self, guild_id: int, role_type: str, role_id: int) -> bool:
        try:
            connection = self.mysql_pool.get_connection()
            cursor = connection.cursor()
            
            valid_roles = ['auto_role', 'premium_role', 'mute_role']
            if role_type not in valid_roles:
                return False
            
            cursor.execute(
                f"UPDATE guild_settings SET {role_type} = %s WHERE guild_id = %s",
                (role_id, guild_id)
            )
            
            cursor.close()
            connection.close()
            return True
        except Exception as e:
            print(f"Error updating guild role: {e}")
            return False
    
    def get_guild_modules(self, guild_id: int) -> List[Dict]:
        connection = self.mysql_pool.get_connection()
        cursor = connection.cursor(dictionary=True)
        
        cursor.execute(
            "SELECT * FROM guild_modules WHERE guild_id = %s",
            (guild_id,)
        )
        
        modules = cursor.fetchall()
        cursor.close()
        connection.close()
        
        return modules
    
    def toggle_module(self, guild_id: int, module_name: str, enabled: bool) -> bool:
        try:
            connection = self.mysql_pool.get_connection()
            cursor = connection.cursor()
            
            cursor.execute("""
                INSERT INTO guild_modules (guild_id, module_name, enabled)
                VALUES (%s, %s, %s)
                ON DUPLICATE KEY UPDATE
                enabled = VALUES(enabled)
            """, (guild_id, module_name, enabled))
            
            cursor.close()
            connection.close()
            return True
        except Exception as e:
            print(f"Error toggling module: {e}")
            return False

    # Reaction Roles Methods
    def add_reaction_role(self, guild_id: int, channel_id: int, message_id: int, emoji: str, role_id: int, created_by: int) -> bool:
        connection = None
        try:
            # Input validation
            from utils.validators import Validators, ValidationError
            Validators.validate_discord_id(guild_id)
            Validators.validate_discord_id(channel_id)
            Validators.validate_discord_id(message_id)
            Validators.validate_discord_id(role_id)
            Validators.validate_discord_id(created_by)
            Validators.validate_emoji(emoji)

            connection = self.mysql_pool.get_connection()
            cursor = connection.cursor()

            cursor.execute("""
                INSERT INTO reaction_roles (guild_id, channel_id, message_id, emoji, role_id, created_by)
                VALUES (%s, %s, %s, %s, %s, %s)
            """, (guild_id, channel_id, message_id, emoji, role_id, created_by))

            cursor.close()
            return True
        except ValidationError as e:
            print(f"Validation error in add_reaction_role: {e}")
            return False
        except Exception as e:
            print(f"Error adding reaction role: {e}")
            return False
        finally:
            if connection:
                connection.close()

    def get_reaction_role(self, guild_id: int, channel_id: int, message_id: int, emoji: str) -> Optional[Dict]:
        connection = None
        try:
            # Input validation
            from utils.validators import Validators, ValidationError
            Validators.validate_discord_id(guild_id)
            Validators.validate_discord_id(channel_id)
            Validators.validate_discord_id(message_id)
            Validators.validate_emoji(emoji)

            connection = self.mysql_pool.get_connection()
            cursor = connection.cursor(dictionary=True)

            cursor.execute("""
                SELECT * FROM reaction_roles
                WHERE guild_id = %s AND channel_id = %s AND message_id = %s AND emoji = %s
            """, (guild_id, channel_id, message_id, emoji))

            reaction_role = cursor.fetchone()
            cursor.close()
            return reaction_role
        except ValidationError as e:
            print(f"Validation error in get_reaction_role: {e}")
            return None
        except Exception as e:
            print(f"Database error in get_reaction_role: {e}")
            return None
        finally:
            if connection:
                connection.close()

    def remove_reaction_role(self, guild_id: int, channel_id: int, message_id: int, emoji: str) -> bool:
        try:
            connection = self.mysql_pool.get_connection()
            cursor = connection.cursor()

            cursor.execute("""
                DELETE FROM reaction_roles
                WHERE guild_id = %s AND channel_id = %s AND message_id = %s AND emoji = %s
            """, (guild_id, channel_id, message_id, emoji))

            cursor.close()
            connection.close()
            return True
        except Exception as e:
            print(f"Error removing reaction role: {e}")
            return False

    def remove_all_reaction_roles(self, guild_id: int, channel_id: int, message_id: int) -> bool:
        try:
            connection = self.mysql_pool.get_connection()
            cursor = connection.cursor()

            cursor.execute("""
                DELETE FROM reaction_roles
                WHERE guild_id = %s AND channel_id = %s AND message_id = %s
            """, (guild_id, channel_id, message_id))

            cursor.close()
            connection.close()
            return True
        except Exception as e:
            print(f"Error removing all reaction roles: {e}")
            return False

    def get_all_reaction_roles(self, guild_id: int) -> List[Dict]:
        connection = self.mysql_pool.get_connection()
        cursor = connection.cursor(dictionary=True)

        cursor.execute("""
            SELECT * FROM reaction_roles WHERE guild_id = %s ORDER BY created_at DESC
        """, (guild_id,))

        reaction_roles = cursor.fetchall()
        cursor.close()
        connection.close()

        return reaction_roles

    def cleanup_deleted_role(self, role_id: int) -> bool:
        try:
            connection = self.mysql_pool.get_connection()
            cursor = connection.cursor()

            cursor.execute("DELETE FROM reaction_roles WHERE role_id = %s", (role_id,))
            cursor.execute("DELETE FROM button_roles WHERE role_id = %s", (role_id,))

            cursor.close()
            connection.close()
            return True
        except Exception as e:
            print(f"Error cleaning up deleted role: {e}")
            return False

    # Button Roles Methods
    def add_button_role(self, guild_id: int, channel_id: int, message_id: int, role_id: int,
                       color: str, emoji: str, label: str, index: int, created_by: int) -> bool:
        try:
            connection = self.mysql_pool.get_connection()
            cursor = connection.cursor()

            cursor.execute("""
                INSERT INTO button_roles (guild_id, channel_id, message_id, role_id, button_color,
                                        button_emoji, button_label, button_index, created_by)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (guild_id, channel_id, message_id, role_id, color, emoji, label, index, created_by))

            cursor.close()
            connection.close()
            return True
        except Exception as e:
            print(f"Error adding button role: {e}")
            return False

    def get_button_roles(self, guild_id: int, channel_id: int, message_id: int) -> List[Dict]:
        connection = self.mysql_pool.get_connection()
        cursor = connection.cursor(dictionary=True)

        cursor.execute("""
            SELECT * FROM button_roles
            WHERE guild_id = %s AND channel_id = %s AND message_id = %s
            ORDER BY button_index
        """, (guild_id, channel_id, message_id))

        button_roles = cursor.fetchall()
        cursor.close()
        connection.close()

        return button_roles

    def remove_button_role(self, guild_id: int, channel_id: int, message_id: int, index: int) -> bool:
        try:
            connection = self.mysql_pool.get_connection()
            cursor = connection.cursor()

            cursor.execute("""
                DELETE FROM button_roles
                WHERE guild_id = %s AND channel_id = %s AND message_id = %s AND button_index = %s
            """, (guild_id, channel_id, message_id, index))

            cursor.close()
            connection.close()
            return True
        except Exception as e:
            print(f"Error removing button role: {e}")
            return False

    def remove_all_button_roles(self, guild_id: int, channel_id: int, message_id: int) -> bool:
        try:
            connection = self.mysql_pool.get_connection()
            cursor = connection.cursor()

            cursor.execute("""
                DELETE FROM button_roles
                WHERE guild_id = %s AND channel_id = %s AND message_id = %s
            """, (guild_id, channel_id, message_id))

            cursor.close()
            connection.close()
            return True
        except Exception as e:
            print(f"Error removing all button roles: {e}")
            return False

    def get_all_button_roles(self, guild_id: int) -> List[Dict]:
        connection = self.mysql_pool.get_connection()
        cursor = connection.cursor(dictionary=True)

        cursor.execute("""
            SELECT * FROM button_roles WHERE guild_id = %s ORDER BY created_at DESC
        """, (guild_id,))

        button_roles = cursor.fetchall()
        cursor.close()
        connection.close()

        return button_roles
