"""
Test suite for the moderation system.
Tests time parsing, duration formatting, and moderation logic.
"""

import pytest
from unittest.mock import Mock, AsyncMock
from cogs.moderate.moderation import Moderation

class TestTimeParsingAndFormatting:
    """Test time parsing and formatting functionality"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.bot = Mock()
        self.moderation = Moderation(self.bot)
    
    def test_parse_duration_valid_inputs(self):
        """Test parsing valid duration strings"""
        test_cases = [
            ("1m", 60),
            ("5m", 300),
            ("1h", 3600),
            ("2h30m", 9000),
            ("1d", 86400),
            ("1w", 604800),
            ("30s", 60),  # Minimum 1 minute
            ("10", 600),  # Default to minutes
        ]
        
        for duration_str, expected_seconds in test_cases:
            result = self.moderation.parse_duration(duration_str)
            assert result == expected_seconds, f"Failed for {duration_str}"
    
    def test_parse_duration_invalid_inputs(self):
        """Test parsing invalid duration strings"""
        invalid_inputs = [
            "",
            None,
            "invalid",
            "abc",
            "1x",  # Invalid unit
        ]
        
        for invalid_input in invalid_inputs:
            result = self.moderation.parse_duration(invalid_input)
            # Should return default (10 minutes)
            assert result == 600
    
    def test_parse_duration_limits(self):
        """Test duration limits (min/max)"""
        # Test minimum (should be 60 seconds)
        result = self.moderation.parse_duration("30s")
        assert result == 60
        
        # Test maximum (should be capped at 28 days)
        result = self.moderation.parse_duration("30d")
        assert result == 2419200  # 28 days in seconds
    
    def test_format_duration(self):
        """Test duration formatting"""
        test_cases = [
            (30, "30s"),
            (60, "1m"),
            (90, "1m 30s"),
            (3600, "1h"),
            (3660, "1h 1m"),
            (86400, "1d"),
            (90000, "1d 1h"),
        ]
        
        for seconds, expected in test_cases:
            result = self.moderation.format_duration(seconds)
            assert result == expected, f"Failed for {seconds} seconds"

class TestModerationPermissions:
    """Test moderation permission checking"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.bot = Mock()
        self.moderation = Moderation(self.bot)
        
        # Mock context
        self.ctx = Mock()
        self.ctx.guild = Mock()
        self.ctx.author = Mock()
        
        # Mock members
        self.target_member = Mock()
        self.bot_member = Mock()
        
        self.ctx.guild.me = self.bot_member
    
    async def test_check_moderation_permissions_hierarchy(self):
        """Test role hierarchy checking"""
        # Setup role hierarchy
        author_role = Mock()
        author_role.position = 5
        target_role = Mock()
        target_role.position = 10
        
        self.ctx.author.top_role = author_role
        self.target_member.top_role = target_role
        
        # Mock bot owner check
        self.bot.is_owner = AsyncMock(return_value=False)
        
        # Should fail due to hierarchy
        result = await self.moderation.check_moderation_permissions(self.ctx, self.target_member)
        assert result is False
    
    async def test_check_moderation_permissions_bot_hierarchy(self):
        """Test bot role hierarchy checking"""
        # Setup role hierarchy where bot can't moderate target
        bot_role = Mock()
        bot_role.position = 5
        target_role = Mock()
        target_role.position = 10
        
        self.bot_member.top_role = bot_role
        self.target_member.top_role = target_role
        
        # Author has higher role than target
        author_role = Mock()
        author_role.position = 15
        self.ctx.author.top_role = author_role
        
        # Mock bot owner check
        self.bot.is_owner = AsyncMock(return_value=False)
        
        # Should fail due to bot hierarchy
        result = await self.moderation.check_moderation_permissions(self.ctx, self.target_member)
        assert result is False
    
    async def test_check_moderation_permissions_self_moderation(self):
        """Test prevention of self-moderation"""
        # Target is the same as author
        result = await self.moderation.check_moderation_permissions(self.ctx, self.ctx.author)
        assert result is False
    
    async def test_check_moderation_permissions_valid(self):
        """Test valid moderation scenario"""
        # Setup valid hierarchy
        author_role = Mock()
        author_role.position = 10
        target_role = Mock()
        target_role.position = 5
        bot_role = Mock()
        bot_role.position = 15
        
        self.ctx.author.top_role = author_role
        self.target_member.top_role = target_role
        self.bot_member.top_role = bot_role
        
        # Different members
        self.target_member.id = 123
        self.ctx.author.id = 456
        
        # Mock bot owner check
        self.bot.is_owner = AsyncMock(return_value=False)
        
        # Should succeed
        result = await self.moderation.check_moderation_permissions(self.ctx, self.target_member)
        assert result is True

if __name__ == "__main__":
    pytest.main([__file__])
