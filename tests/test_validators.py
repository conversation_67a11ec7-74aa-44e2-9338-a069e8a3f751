"""
Test suite for the validators module.
Tests all input validation functionality.
"""

import pytest
from utils.validators import Validators, ValidationError

class TestDiscordIDValidation:
    """Test Discord ID validation"""
    
    def test_valid_discord_ids(self):
        """Test valid Discord IDs"""
        valid_ids = [
            "123456789012345678",  # 18 digits
            "1234567890123456789",  # 19 digits
            "12345678901234567890",  # 20 digits
            "123456789012345678901",  # 21 digits
            123456789012345678,  # Integer
        ]
        
        for valid_id in valid_ids:
            result = Validators.validate_discord_id(valid_id)
            assert isinstance(result, int)
            assert result > 0
    
    def test_invalid_discord_ids(self):
        """Test invalid Discord IDs"""
        invalid_ids = [
            "12345",  # Too short
            "1234567890123456789012",  # Too long
            "abc123456789012345",  # Contains letters
            "",  # Empty string
            None,  # None value
            -123456789012345678,  # Negative
        ]
        
        for invalid_id in invalid_ids:
            with pytest.raises(ValidationError):
                Validators.validate_discord_id(invalid_id)
    
    def test_mention_format_ids(self):
        """Test Discord IDs in mention format"""
        mention_formats = [
            "<@123456789012345678>",  # User mention
            "<@!123456789012345678>",  # Nickname mention
            "<#123456789012345678>",  # Channel mention
            "<&123456789012345678>",  # Role mention
        ]
        
        for mention in mention_formats:
            result = Validators.validate_discord_id(mention)
            assert result == 123456789012345678

class TestMessageLinkValidation:
    """Test Discord message link validation"""
    
    def test_valid_message_links(self):
        """Test valid Discord message links"""
        valid_links = [
            "https://discord.com/channels/123456789012345678/987654321098765432/111222333444555666",
            "https://discordapp.com/channels/123456789012345678/987654321098765432/111222333444555666",
            "https://ptb.discord.com/channels/123456789012345678/987654321098765432/111222333444555666",
            "https://canary.discord.com/channels/123456789012345678/987654321098765432/111222333444555666",
        ]
        
        for link in valid_links:
            guild_id, channel_id, message_id = Validators.validate_message_link(link)
            assert guild_id == 123456789012345678
            assert channel_id == 987654321098765432
            assert message_id == 111222333444555666
    
    def test_invalid_message_links(self):
        """Test invalid Discord message links"""
        invalid_links = [
            "https://discord.com/channels/@me/987654321098765432/111222333444555666",  # DM
            "https://example.com/channels/123/456/789",  # Wrong domain
            "https://discord.com/channels/123/456",  # Missing message ID
            "not_a_url",  # Not a URL
            "",  # Empty string
        ]
        
        for link in invalid_links:
            with pytest.raises(ValidationError):
                Validators.validate_message_link(link)

class TestEmojiValidation:
    """Test emoji validation"""
    
    def test_valid_emojis(self):
        """Test valid emoji formats"""
        valid_emojis = [
            "<:test:123456789012345678>",  # Custom emoji
            "<a:animated:123456789012345678>",  # Animated emoji
            "😀",  # Unicode emoji
            "👍",  # Unicode emoji
            "🎉",  # Unicode emoji
        ]
        
        for emoji in valid_emojis:
            result = Validators.validate_emoji(emoji)
            assert result == emoji
    
    def test_invalid_emojis(self):
        """Test invalid emoji formats"""
        invalid_emojis = [
            "<:invalid>",  # Missing ID
            "<:test:abc>",  # Invalid ID
            "",  # Empty string
            "abc",  # Plain text
            "123",  # Numbers only
        ]
        
        for emoji in invalid_emojis:
            with pytest.raises(ValidationError):
                Validators.validate_emoji(emoji)

class TestStringLengthValidation:
    """Test string length validation"""
    
    def test_valid_string_lengths(self):
        """Test strings within length limits"""
        test_cases = [
            ("short", 10),
            ("exactly ten", 10),
            ("", 5),  # Empty string
        ]
        
        for text, max_length in test_cases:
            result = Validators.validate_string_length(text, max_length)
            assert result == text
    
    def test_invalid_string_lengths(self):
        """Test strings exceeding length limits"""
        test_cases = [
            ("this is too long", 5),
            ("x" * 1000, 100),
        ]
        
        for text, max_length in test_cases:
            with pytest.raises(ValidationError):
                Validators.validate_string_length(text, max_length)

class TestButtonColorValidation:
    """Test button color validation"""
    
    def test_valid_button_colors(self):
        """Test valid button colors"""
        valid_colors = ["blue", "gray", "grey", "green", "red"]
        
        for color in valid_colors:
            result = Validators.validate_button_color(color)
            assert result in ["blue", "gray", "green", "red"]
    
    def test_invalid_button_colors(self):
        """Test invalid button colors"""
        invalid_colors = ["purple", "yellow", "orange", "", "invalid"]
        
        for color in invalid_colors:
            with pytest.raises(ValidationError):
                Validators.validate_button_color(color)
    
    def test_color_normalization(self):
        """Test color normalization (grey -> gray)"""
        result = Validators.validate_button_color("grey")
        assert result == "gray"

class TestURLValidation:
    """Test URL validation"""
    
    def test_valid_urls(self):
        """Test valid URLs"""
        valid_urls = [
            "https://example.com",
            "http://test.org",
            "https://discord.com/channels/123/456/789",
            "https://github.com/user/repo",
        ]
        
        for url in valid_urls:
            result = Validators.validate_url(url)
            assert result == url
    
    def test_invalid_urls(self):
        """Test invalid URLs"""
        invalid_urls = [
            "not_a_url",
            "ftp://example.com",  # Wrong scheme
            "",  # Empty string
            "javascript:alert('xss')",  # Potentially dangerous
        ]
        
        for url in invalid_urls:
            with pytest.raises(ValidationError):
                Validators.validate_url(url)

class TestSQLSanitization:
    """Test SQL input sanitization"""
    
    def test_sql_sanitization(self):
        """Test SQL input sanitization"""
        test_cases = [
            ("normal text", "normal text"),
            ("text\x00with\x00nulls", "textwithNulls"),
            ("line\nbreaks\rhere", "line breaks here"),
            ("x" * 2000, "x" * 1000),  # Length limiting
        ]
        
        for input_text, expected in test_cases:
            result = Validators.sanitize_sql_input(input_text)
            assert len(result) <= 1000
            assert "\x00" not in result
            assert "\r" not in result

if __name__ == "__main__":
    pytest.main([__file__])
