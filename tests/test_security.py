"""
Test suite for the security system.
Tests rate limiting, permission escalation detection, and security auditing.
"""

import pytest
import time
from unittest.mock import Mock
from utils.security import SecurityAudit, RoleSecurityChecker, InputSanitizer
import discord

class TestSecurityAudit:
    """Test security audit functionality"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.audit = SecurityAudit()
    
    def test_rate_limit_normal_usage(self):
        """Test normal command usage within limits"""
        user_id = 12345
        
        # Should allow normal usage
        for i in range(25):  # Under default limit of 30
            result = self.audit.check_rate_limit(user_id, "test_command")
            assert result is True
    
    def test_rate_limit_exceeded(self):
        """Test rate limiting when exceeded"""
        user_id = 12345
        
        # Fill up the rate limit
        for i in range(30):
            self.audit.check_rate_limit(user_id, "test_command")
        
        # Next command should be rate limited
        result = self.audit.check_rate_limit(user_id, "test_command")
        assert result is False
    
    def test_rate_limit_time_window(self):
        """Test rate limit time window behavior"""
        user_id = 12345
        
        # Mock time to test time window
        original_time = time.time
        mock_time = Mock(return_value=1000.0)
        time.time = mock_time
        
        try:
            # Fill rate limit
            for i in range(30):
                self.audit.check_rate_limit(user_id, "test_command")
            
            # Should be rate limited
            assert self.audit.check_rate_limit(user_id, "test_command") is False
            
            # Advance time by 61 seconds
            mock_time.return_value = 1061.0
            
            # Should be allowed again
            assert self.audit.check_rate_limit(user_id, "test_command") is True
            
        finally:
            time.time = original_time
    
    def test_permission_escalation_detection(self):
        """Test permission escalation attempt detection"""
        user_id = 12345
        guild_id = 67890
        
        # Normal permission should be fine
        result = self.audit.check_permission_escalation(user_id, "send_messages", guild_id)
        assert result is True
        
        # Multiple dangerous permissions should trigger detection
        dangerous_perms = ["administrator", "manage_guild", "ban_members"]
        
        for perm in dangerous_perms:
            self.audit.check_permission_escalation(user_id, perm, guild_id)
        
        # Should be flagged as suspicious
        assert self.audit.is_suspicious_user(user_id) is True
    
    def test_validation_failure_tracking(self):
        """Test validation failure tracking"""
        user_id = 12345
        guild_id = 67890
        
        # Log multiple validation failures
        for i in range(12):  # Over the threshold of 10
            self.audit.log_validation_failure(user_id, "discord_id", "invalid_input", guild_id)
        
        # Should be marked as suspicious
        assert self.audit.is_suspicious_user(user_id) is True
    
    def test_clear_user_flags(self):
        """Test clearing user security flags"""
        user_id = 12345
        
        # Make user suspicious
        for i in range(12):
            self.audit.log_validation_failure(user_id, "test", "invalid")
        
        assert self.audit.is_suspicious_user(user_id) is True
        
        # Clear flags
        self.audit.clear_user_flags(user_id)
        
        assert self.audit.is_suspicious_user(user_id) is False
    
    def test_get_user_stats(self):
        """Test getting user security statistics"""
        user_id = 12345
        
        # Generate some activity
        self.audit.check_rate_limit(user_id, "test")
        self.audit.log_validation_failure(user_id, "test", "invalid")
        
        stats = self.audit.get_user_stats(user_id)
        
        assert isinstance(stats, dict)
        assert 'suspicious' in stats
        assert 'failed_attempts' in stats
        assert 'validation_failures' in stats
        assert 'recent_commands' in stats
        assert 'permission_attempts' in stats

class TestRoleSecurityChecker:
    """Test role security checking"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.checker = RoleSecurityChecker()
    
    def test_managed_role_check(self):
        """Test managed role detection"""
        # Mock managed role
        role = Mock()
        role.managed = True
        
        is_safe, error_msg = self.checker.check_role_safety(role, "assign")
        
        assert is_safe is False
        assert "managed roles" in error_msg.lower()
    
    def test_dangerous_permissions_check(self):
        """Test dangerous permissions detection"""
        # Mock role with dangerous permissions
        role = Mock()
        role.managed = False
        role.permissions = Mock()
        
        # Set dangerous permission
        role.permissions.administrator = True
        role.permissions.manage_guild = False
        role.permissions.ban_members = False
        
        is_safe, error_msg = self.checker.check_role_safety(role, "assign")
        
        assert is_safe is False
        assert "dangerous permissions" in error_msg.lower()
    
    def test_safe_role_check(self):
        """Test safe role validation"""
        # Mock safe role
        role = Mock()
        role.managed = False
        role.permissions = Mock()
        role.position = 5
        
        # Set all dangerous permissions to False
        for perm in ["administrator", "manage_guild", "ban_members", "kick_members", 
                     "moderate_members", "manage_roles", "manage_channels", "manage_messages"]:
            setattr(role.permissions, perm, False)
        
        is_safe, error_msg = self.checker.check_role_safety(role, "assign")
        
        assert is_safe is True
        assert error_msg is None
    
    def test_role_hierarchy_bot_check(self):
        """Test bot role hierarchy checking"""
        # Mock objects
        member = Mock()
        role = Mock()
        bot_member = Mock()
        
        # Bot role lower than target role
        bot_role = Mock()
        bot_role.position = 5
        bot_member.top_role = bot_role
        role.position = 10
        
        is_safe, error_msg = self.checker.check_role_hierarchy(member, role, bot_member)
        
        assert is_safe is False
        assert "role hierarchy" in error_msg.lower()
    
    def test_role_hierarchy_user_check(self):
        """Test user role hierarchy checking"""
        # Mock objects
        member = Mock()
        role = Mock()
        bot_member = Mock()
        guild = Mock()
        
        # Setup hierarchy where user can't assign role
        member.guild = guild
        guild.owner = Mock()  # Different from member
        
        user_role = Mock()
        user_role.position = 5
        member.top_role = user_role
        role.position = 10
        
        # Bot can manage the role
        bot_role = Mock()
        bot_role.position = 15
        bot_member.top_role = bot_role
        
        is_safe, error_msg = self.checker.check_role_hierarchy(member, role, bot_member)
        
        assert is_safe is False
        assert "higher than your highest role" in error_msg

class TestInputSanitizer:
    """Test input sanitization"""
    
    def test_sanitize_text_input(self):
        """Test text input sanitization"""
        test_cases = [
            ("normal text", "normal text"),
            ("text\x00with\x00nulls", "textwithNulls"),
            ("line\nbreaks\r\nhere", "line breaks here"),
            ("  excessive   whitespace  ", "excessive whitespace"),
            ("x" * 2000, "x" * 1000),  # Length limiting
        ]
        
        for input_text, expected in test_cases:
            result = InputSanitizer.sanitize_text_input(input_text)
            assert len(result) <= 1000
            assert "\x00" not in result
            assert "\r" not in result
            # Check whitespace normalization
            assert "  " not in result or result == expected
    
    def test_injection_detection(self):
        """Test injection attempt detection"""
        suspicious_inputs = [
            "<script>alert('xss')</script>",
            "javascript:alert('xss')",
            "data:text/html,<script>alert('xss')</script>",
            "eval('malicious code')",
            "exec('rm -rf /')",
            "__import__('os').system('rm -rf /')",
        ]
        
        for suspicious_input in suspicious_inputs:
            result = InputSanitizer.check_for_injection_attempts(suspicious_input)
            assert result is True, f"Failed to detect: {suspicious_input}"
    
    def test_safe_input_detection(self):
        """Test that safe inputs are not flagged"""
        safe_inputs = [
            "Hello world!",
            "This is a normal message",
            "User123 said something",
            "https://example.com",
            "Some emoji 😀 here",
        ]
        
        for safe_input in safe_inputs:
            result = InputSanitizer.check_for_injection_attempts(safe_input)
            assert result is False, f"False positive for: {safe_input}"

if __name__ == "__main__":
    pytest.main([__file__])
