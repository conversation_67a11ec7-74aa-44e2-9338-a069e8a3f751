# Import essential modules
import discord
from discord.ext import commands
import os
import asyncio
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import bot modules
from database.database import db
from utils.config import config, colors, emotes
from utils.permissions import Permission<PERSON>heck, PermissionErrorHandler
from embeds.prebuilt import embeds
from embeds.parse import create_embed
from embeds.button import create_pagination_view
from utils.logger import startup_info, info, error, cache_info
from utils.error_handler import setup_global_error_handler
from utils.config_manager import load_config, get_config

# Load and validate configuration
if not load_config():
    print("Failed to load configuration. Exiting.")
    exit(1)

config_mgr = get_config()
TOKEN = config_mgr.bot.token
PREFIX = config_mgr.bot.prefix

intents = discord.Intents.all()
bot = commands.Bot(command_prefix=PREFIX, intents=intents, help_command=None)

@bot.event
async def on_ready():
    startup_info("Bot", f"Ready! Logged in as {bot.user} (ID: {bot.user.id})")
    startup_info("Guilds", f"Connected to {len(bot.guilds)} guilds")
    startup_info("Prefix", f"Using prefix: {PREFIX}")

    # Start cache cleanup task
    bot.loop.create_task(cache_cleanup_task())
    startup_info("Cache", "Cleanup task started")

@bot.command()
async def ping(ctx):
    await ctx.send(f'Pong! `{round(bot.latency * 1000)}ms`')

@bot.command()
async def cache_stats(ctx):
    if not await bot.is_owner(ctx.author):
        return

    stats = db.get_cache_stats()
    await ctx.send(f"Cache Stats: {stats}")

async def cache_cleanup_task():
    while True:
        await asyncio.sleep(1800)  # 30 minutes
        try:
            await db.cleanup_cache()
            cache_info("Cleanup completed")
        except Exception as e:
            error("Cache cleanup failed", e)

async def load_cogs():
    # Load cogs
    cogs_path = Path('./cogs')
    if cogs_path.exists():
        for file in cogs_path.rglob('*.py'):
            if file.name.startswith('_'):
                continue

            relative_path = file.relative_to(cogs_path)
            cog_name = f'cogs.{str(relative_path.with_suffix("")).replace("/", ".")}'
            try:
                await bot.load_extension(cog_name)
                startup_info("Cog", f"Loaded {cog_name}")
            except Exception as e:
                error(f"Failed to load cog {cog_name}", e)

    # Load events
    events_path = Path('./events')
    if events_path.exists():
        for file in events_path.rglob('*.py'):
            if file.name.startswith('_'):
                continue

            relative_path = file.relative_to(events_path)
            event_name = f'events.{str(relative_path.with_suffix("")).replace("/", ".")}'
            try:
                await bot.load_extension(event_name)
                startup_info("Event", f"Loaded {event_name}")
            except Exception as e:
                error(f"Failed to load event {event_name}", e)

async def load_extensions():
    try:
        await bot.load_extension('jishaku')
        startup_info("Extension", "Loaded jishaku")
    except Exception as e:
        error("Failed to load jishaku", e)

    await load_cogs()

async def main():
    async with bot:
        # Setup global error handler
        error_handler = setup_global_error_handler(bot)
        startup_info("Error Handler", "Global error handler configured")

        await db.connect()
        await load_extensions()
        await bot.start(TOKEN)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("Bot stopped")
    except Exception as e:
        print(f"Failed to start: {e}")
