# Import essential modules
import discord
from discord.ext import commands
import os
import asyncio
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import bot modules
from database.database import db
from utils.config import config, colors, emotes
from embeds.prebuilt import embeds
from embeds.parse import create_embed
from embeds.button import create_pagination_view

# Bot configuration
TOKEN = os.getenv('DISCORD_TOKEN')
PREFIX = os.getenv('DISCORD_PREFIX', ',')

intents = discord.Intents.all()
bot = commands.Bot(command_prefix=PREFIX, intents=intents, help_command=None)

@bot.event
async def on_ready():
    print(f'Bot ready! Logged in as {bot.user} (ID: {bot.user.id})')
    print(f'Connected to {len(bot.guilds)} guilds')
    print(f'Prefix: {PREFIX}')

    # Start cache cleanup task
    bot.loop.create_task(cache_cleanup_task())

@bot.event
async def on_message(message):
    # Don't respond to bots
    if message.author.bot:
        return

    # Process commands
    await bot.process_commands(message)

@bot.event
async def on_command_error(ctx, error):
    """Global error handler for all commands"""
    from utils import permissions

    if isinstance(error, commands.CheckFailure):
        # Handle permission errors globally
        if isinstance(error, commands.NotOwner):
            await permissions.HandlePermissionError(ctx, "owner")
        else:
            await permissions.HandlePermissionError(ctx, "permission")
    elif isinstance(error, commands.CommandNotFound):
        # Ignore command not found errors
        pass
    elif isinstance(error, commands.MissingRequiredArgument):
        # Show help for commands with missing arguments automatically
        help_cog = bot.get_cog('Help')
        if help_cog and ctx.command:
            await help_cog.show_command_help(ctx, ctx.command)
    elif isinstance(error, commands.BadArgument):
        # Also show help for bad arguments (like invalid member)
        help_cog = bot.get_cog('Help')
        if help_cog and ctx.command:
            await help_cog.show_command_help(ctx, ctx.command)
    elif isinstance(error, commands.CommandInvokeError):
        # Log command invoke errors but don't show to user
        print(f"Command invoke error in {ctx.command}: {error.original}")
    else:
        # Log other errors
        print(f"Unhandled error in {ctx.command}: {error}")

@bot.command()
async def ping(ctx):
    await ctx.send(f'Pong! `{round(bot.latency * 1000)}ms`')

@bot.command()
async def cache_stats(ctx):
    if not await bot.is_owner(ctx.author):
        return

    stats = db.get_cache_stats()
    await ctx.send(f"Cache Stats: {stats}")

async def cache_cleanup_task():
    while True:
        await asyncio.sleep(1800)  # 30 minutes
        try:
            await db.cleanup_cache()
            print("Cache cleanup completed")
        except Exception as e:
            print(f"Cache cleanup failed: {e}")

async def load_cogs():
    # Load cogs
    cogs_path = Path('./cogs')
    if cogs_path.exists():
        for file in cogs_path.rglob('*.py'):
            if file.name.startswith('_'):
                continue

            relative_path = file.relative_to(cogs_path)
            cog_name = f'cogs.{str(relative_path.with_suffix("")).replace("/", ".")}'
            try:
                await bot.load_extension(cog_name)
                print(f'Loaded: {cog_name}')
            except Exception as e:
                print(f'Failed to load {cog_name}: {e}')

    # Load events
    events_path = Path('./events')
    if events_path.exists():
        for file in events_path.rglob('*.py'):
            if file.name.startswith('_'):
                continue

            relative_path = file.relative_to(events_path)
            event_name = f'events.{str(relative_path.with_suffix("")).replace("/", ".")}'
            try:
                await bot.load_extension(event_name)
                print(f'Loaded: {event_name}')
            except Exception as e:
                print(f'Failed to load {event_name}: {e}')

async def load_extensions():
    try:
        await bot.load_extension('jishaku')
        print('Loaded: jishaku')
    except Exception as e:
        print(f'Failed to load jishaku: {e}')

    await load_cogs()

async def setup_globals():
    """Setup global utilities that can be used without imports"""
    import builtins

    # Import utilities
    from utils import permissions, config, finder

    # Make them globally available
    builtins.permissions = permissions
    builtins.config = config
    builtins.finder = finder
    builtins.embeds = embeds
    builtins.db = db

    print("Global utilities setup complete")

async def main():
    async with bot:
        await setup_globals()
        await db.connect()
        await load_extensions()
        await bot.start(TOKEN)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("Bot stopped")
    except Exception as e:
        print(f"Failed to start: {e}")
