# Blur Discord Bot

A comprehensive Discord moderation and utility bot with advanced security features, caching, and robust error handling.

## 🚀 Features

### Core Functionality
- **Moderation System**: Timeout/mute, kick, ban/unban with proper time parsing
- **Role Management**: Button roles and reaction roles with security checks
- **Embed System**: Advanced embed creation and management
- **Help System**: Interactive help with pagination and command details
- **Owner Commands**: Administrative tools and debugging utilities

### Security & Performance
- **Input Validation**: Comprehensive validation for all user inputs
- **Rate Limiting**: Prevents spam and abuse
- **Permission Escalation Detection**: Monitors suspicious permission attempts
- **Caching System**: Redis-based caching with fallback to FakeRedis
- **Database Connection Pooling**: Efficient MySQL connection management
- **Health Monitoring**: System health checks and diagnostics

### Advanced Features
- **Error Tracking**: Centralized error logging with trace codes
- **Security Audit**: Real-time security monitoring and alerts
- **Configuration Management**: Environment-based configuration with validation
- **Logging System**: Structured logging with multiple levels and file rotation

## 📋 Requirements

### System Requirements
- Python 3.9+
- MySQL 8.0+
- Redis 6.0+ (optional, FakeRedis fallback available)
- Linux VPS (recommended)

### Python Dependencies
See `install.txt` for the complete list of dependencies.

## 🛠️ Installation

### 1. Clone the Repository
```bash
git clone <repository-url>
cd blur
```

### 2. Create Virtual Environment
```bash
python3 -m venv venv
source venv/bin/activate  # On Linux/Mac
# or
venv\Scripts\activate  # On Windows
```

### 3. Install Dependencies
```bash
pip install -r install.txt
```

### 4. Database Setup
Create a MySQL database and user:
```sql
CREATE DATABASE blur_bot;
CREATE USER 'blur_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON blur_bot.* TO 'blur_user'@'localhost';
FLUSH PRIVILEGES;
```

### 5. Environment Configuration
Create a `.env` file in the root directory:
```env
# Discord Configuration
DISCORD_TOKEN=your_bot_token_here
DISCORD_PREFIX=,

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=blur_user
DB_PASSWORD=your_password
DB_NAME=blur_bot
DB_POOL_SIZE=15

# Redis Configuration (optional)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# Bot Configuration
DEV_SERVER_ID=your_dev_server_id
DEV_ROLE_ID=your_dev_role_id
PREMIUM_ROLE_ID=your_premium_role_id
SUPPORT_SERVER=https://discord.gg/your_invite

# Security Configuration
MAX_COMMANDS_PER_MINUTE=30
ENABLE_SECURITY_AUDIT=true

# Logging Configuration
DEBUG_MODE=false
VERBOSE_LOGGING=false
```

### 6. Run the Bot
```bash
python main.py
```

## 🔧 Configuration

### Environment Variables

#### Required Variables
- `DISCORD_TOKEN`: Your Discord bot token
- `DB_HOST`: MySQL database host
- `DB_USER`: MySQL database username
- `DB_PASSWORD`: MySQL database password
- `DB_NAME`: MySQL database name

#### Optional Variables
- `DISCORD_PREFIX`: Command prefix (default: `,`)
- `DB_PORT`: MySQL port (default: `3306`)
- `DB_POOL_SIZE`: Connection pool size (default: `15`)
- `REDIS_HOST`: Redis host (default: `localhost`)
- `REDIS_PORT`: Redis port (default: `6379`)
- `REDIS_PASSWORD`: Redis password (optional)
- `MAX_COMMANDS_PER_MINUTE`: Rate limit (default: `30`)
- `DEBUG_MODE`: Enable debug logging (default: `false`)
- `VERBOSE_LOGGING`: Enable verbose logging (default: `false`)

## 📚 Usage

### Basic Commands

#### Moderation
- `,mute @user 10m reason` - Timeout a user
- `,unmute @user` - Remove timeout
- `,kick @user reason` - Kick a user
- `,ban @user reason` - Ban a user
- `,unban user_id` - Unban a user

#### Role Management
- `,buttonrole add <message_link> <color> [emoji] [label] <role>` - Add button role
- `,buttonrole remove <message_link> <index>` - Remove button role
- `,reactionrole add <message_link> <emoji> <role>` - Add reaction role

#### Embed System
- `,embed create <embed_code>` - Create custom embed
- `,embed copy <message_link>` - Copy embed from message

#### Utility
- `,help` - Show help menu
- `,ping` - Check bot latency

### Owner Commands
- `,sudo mutuals @user` - Show mutual servers
- `,trace <error_code>` - Get error details
- `,sync` - Sync slash commands

## 🏗️ Architecture

### Directory Structure
```
blur/
├── main.py                 # Bot entry point
├── install.txt            # Dependencies
├── README.md              # Documentation
├── .env                   # Environment variables
├── cogs/                  # Command modules
│   ├── moderate/          # Moderation commands
│   ├── autorole/          # Role management
│   ├── utility/           # Utility commands
│   └── owner/             # Owner-only commands
├── database/              # Database layer
│   ├── database.py        # Main database class
│   ├── models/            # Database models
│   └── redis/             # Redis caching
├── embeds/                # Embed system
│   ├── parse.py           # Embed parsing
│   ├── prebuilt.py        # Prebuilt embeds
│   └── button.py          # Button components
├── events/                # Event handlers
│   ├── guild_events.py    # Guild events
│   ├── reaction_roles.py  # Reaction role events
│   └── button_roles.py    # Button role events
├── utils/                 # Utility modules
│   ├── config.py          # Bot configuration
│   ├── constants.py       # Constants and limits
│   ├── validators.py      # Input validation
│   ├── security.py        # Security systems
│   ├── logger.py          # Logging system
│   ├── error_handler.py   # Error handling
│   ├── health_check.py    # Health monitoring
│   └── permissions.py     # Permission checking
├── tests/                 # Test suite
└── logs/                  # Log files
```

### Key Components

#### Database Layer
- **Connection Pooling**: Efficient MySQL connection management
- **Caching**: Redis-based caching with automatic fallback
- **Models**: Separate models for users, guilds, and role systems
- **Validation**: Input validation at the database layer

#### Security System
- **Rate Limiting**: Per-user command rate limiting
- **Permission Escalation Detection**: Monitors dangerous permission attempts
- **Input Validation**: Comprehensive validation for all inputs
- **Audit Logging**: Security event logging and monitoring

#### Error Handling
- **Global Error Handler**: Centralized error handling for all commands
- **Error Tracking**: Unique error codes for debugging
- **Logging**: Structured logging with multiple levels
- **Health Checks**: System health monitoring and diagnostics

## 🧪 Testing

Run the test suite:
```bash
# Install pytest if not already installed
pip install pytest

# Run all tests
pytest tests/

# Run specific test file
pytest tests/test_validators.py

# Run with coverage
pip install pytest-cov
pytest --cov=utils tests/
```

## 🔒 Security Features

### Input Validation
- Discord ID format validation
- Message link validation
- Emoji format validation
- String length limits
- SQL injection prevention

### Rate Limiting
- Per-user command rate limiting
- Configurable limits
- Time window-based tracking
- Automatic suspicious user flagging

### Permission Security
- Role hierarchy validation
- Dangerous permission detection
- Permission escalation monitoring
- Automatic security alerts

### Audit Logging
- All moderation actions logged
- Security events tracked
- Error tracking with codes
- Performance monitoring

## 📊 Monitoring

### Health Checks
The bot includes comprehensive health monitoring:
- Database connectivity
- Redis/cache status
- Discord API responsiveness
- System resource usage
- Memory usage tracking

### Logging
Structured logging with multiple levels:
- **DEBUG**: Detailed debugging information
- **INFO**: General information and events
- **WARNING**: Warning conditions
- **ERROR**: Error conditions
- **CRITICAL**: Critical errors

Log files are automatically rotated and stored in the `logs/` directory.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Join our Discord server: [Support Server](https://discord.gg/ge8gxgVE9U)
- Create an issue on GitHub
- Check the documentation and logs for troubleshooting

## 🔄 Changelog

### Version 2.0.0 (Current)
- Complete codebase refactor and security overhaul
- Added comprehensive input validation
- Implemented advanced caching system
- Added security audit and monitoring
- Fixed database connection pool issues
- Added health monitoring system
- Improved error handling and logging
- Added comprehensive test suite
- Fixed time parsing in moderation commands
- Implemented button role event handlers
- Added configuration management system
