import discord
from discord.ext import commands
from utils.permissions import Per<PERSON><PERSON><PERSON><PERSON>, PermissionError<PERSON><PERSON><PERSON>
from embeds.prebuilt import embeds
from database.database import db
from utils.finder import FindRole
from utils.validators import Validators, ValidationError

class ReactionRole(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    async def cog_command_error(self, ctx, error):
        if isinstance(error, commands.CheckFailure):
            await Permission<PERSON>rror<PERSON><PERSON><PERSON>(ctx, "permission")

    @commands.group(
        name="reactionrole",
        description="Set up self-assignable roles with reactions",
        usage="reactionrole <subcommand> <args>",
        example="reactionrole add .../channels/... 🌟 @role",
        aliases=['rr']
    )
    @PermissionCheck.discord_permission("ManageGuild")
    async def reactionrole(self, ctx):
        if ctx.invoked_subcommand is None:
            help_cog = self.bot.get_cog('Help')
            if help_cog:
                await help_cog.show_group_help(ctx, ctx.command)

    @reactionrole.command(
        name="add",
        description="Add a reaction role to a message",
        usage="reactionrole add <message_link> <emoji> <role>",
        example="reactionrole add .../channels/... 🌟 @role"
    )
    @PermissionCheck.discord_permission("ManageRoles")
    async def rr_add(self, ctx, message_link: str = None, emoji: str = None, *, role_input: str = None):
        if not all([message_link, emoji, role_input]):
            help_cog = self.bot.get_cog('Help')
            if help_cog:
                await help_cog.show_command_help(ctx, ctx.command)
            return

        # Parse message link
        message, channel = await self.parse_message_link(ctx, message_link)
        if not message:
            return

        # Find role
        role = await FindRole(ctx, role_input)
        if not role:
            await embeds.deny(ctx, f"Could not find role: {role_input}")
            return

        # Simple role checks
        if role >= ctx.author.top_role and not await self.bot.is_owner(ctx.author):
            await embeds.deny(ctx, "You cannot assign a role higher than your highest role.")
            return

        if role >= ctx.guild.me.top_role:
            await embeds.deny(ctx, "I cannot assign a role higher than my highest role.")
            return

        if role.managed:
            await embeds.deny(ctx, "Cannot assign managed roles (bot roles, booster roles, etc.)")
            return

        try:
            # Add reaction to message
            await message.add_reaction(emoji)

            # Store in database
            success = db.guilds.add_reaction_role(
                ctx.guild.id, channel.id, message.id, emoji, role.id, ctx.author.id
            )

            if success:
                await embeds.success(ctx, f"Added reaction role: {emoji} → {role.mention}")
            else:
                await embeds.deny(ctx, "Failed to save reaction role to database.")

        except discord.HTTPException:
            await embeds.deny(ctx, "Failed to add reaction. Invalid emoji or insufficient permissions.")
        except Exception as e:
            await embeds.deny(ctx, f"An error occurred: {str(e)}")

    @reactionrole.command(
        name="remove",
        description="Remove a specific reaction role from a message",
        usage="reactionrole remove <message_link> <emoji>",
        example="reactionrole remove .../channels/... 🌟",
        aliases=['delete', 'del']
    )
    @PermissionCheck.discord_permission("ManageRoles")
    async def rr_remove(self, ctx, message_link: str = None, emoji: str = None):
        if not all([message_link, emoji]):
            help_cog = self.bot.get_cog('Help')
            if help_cog:
                await help_cog.show_command_help(ctx, ctx.command)
            return

        # Parse message link
        message, channel = await self.parse_message_link(ctx, message_link)
        if not message:
            return

        try:
            # Remove reaction from message
            await message.clear_reaction(emoji)

            # Remove from database
            success = db.guilds.remove_reaction_role(
                ctx.guild.id, channel.id, message.id, emoji
            )

            if success:
                await embeds.success(ctx, f"Removed reaction role: {emoji}")
            else:
                await embeds.deny(ctx, "Failed to remove reaction role from database.")

        except discord.HTTPException:
            await embeds.deny(ctx, "Failed to remove reaction. Invalid emoji or insufficient permissions.")
        except Exception as e:
            await embeds.deny(ctx, f"An error occurred: {str(e)}")

    @reactionrole.command(
        name="removeall",
        description="Remove all reaction roles from a message",
        usage="reactionrole removeall <message_link>",
        example="reactionrole removeall .../channels/...",
        aliases=['deleteall', 'delall']
    )
    @PermissionCheck.discord_permission("ManageRoles")
    async def rr_removeall(self, ctx, message_link: str = None):
        if not message_link:
            help_cog = self.bot.get_cog('Help')
            if help_cog:
                await help_cog.show_command_help(ctx, ctx.command)
            return

        # Parse message link
        message, channel = await self.parse_message_link(ctx, message_link)
        if not message:
            return

        try:
            # Clear all reactions from message
            await message.clear_reactions()

            # Remove all from database
            success = db.guilds.remove_all_reaction_roles(
                ctx.guild.id, channel.id, message.id
            )

            if success:
                await embeds.success(ctx, "Removed all reaction roles from the message.")
            else:
                await embeds.deny(ctx, "Failed to remove reaction roles from database.")

        except discord.HTTPException:
            await embeds.deny(ctx, "Failed to clear reactions. Insufficient permissions.")
        except Exception as e:
            await embeds.deny(ctx, f"An error occurred: {str(e)}")

    @reactionrole.command(
        name="list",
        description="List all reaction roles in the server",
        usage="reactionrole list",
        example="reactionrole list"
    )
    @PermissionCheck.discord_permission("ManageRoles")
    async def rr_list(self, ctx):
        # Ensure guild exists in database
        db.guilds.create_guild(ctx.guild.id)
        reaction_roles = db.guilds.get_all_reaction_roles(ctx.guild.id)

        if not reaction_roles:
            await embeds.default(ctx, "No reaction roles found in this server.")
            return

        description_lines = []
        for i, rr in enumerate(reaction_roles[:10], 1):  # Limit to 10 for display
            channel = self.bot.get_channel(rr['channel_id'])
            role = ctx.guild.get_role(rr['role_id'])

            if channel and role:
                description_lines.append(f"`{i}` {rr['emoji']} → {role.mention} in {channel.mention}")
            else:
                description_lines.append(f"`{i}` {rr['emoji']} → *Deleted role/channel*")

        if len(reaction_roles) > 10:
            description_lines.append(f"\n*And {len(reaction_roles) - 10} more...*")

        await embeds.default(ctx, f"**Reaction Roles ({len(reaction_roles)} total):**\n" + "\n".join(description_lines))

    @reactionrole.command(
        name="reset",
        description="Remove all reaction roles from the server",
        usage="reactionrole reset",
        example="reactionrole reset"
    )
    @PermissionCheck.discord_permission("ManageGuild")
    async def rr_reset(self, ctx):
        # This would remove ALL reaction roles from the server - dangerous!
        await embeds.default(ctx, "Reset feature disabled for safety. Use `removeall` on individual messages instead.")

    async def parse_message_link(self, ctx, message_link):
        """Simple message link parser"""
        if not message_link.startswith('https://discord.com/channels/'):
            await embeds.deny(ctx, "Invalid message link format.")
            return None, None

        try:
            parts = message_link.split('/')
            guild_id = int(parts[4])
            channel_id = int(parts[5])
            message_id = int(parts[6])

            if guild_id != ctx.guild.id:
                await embeds.deny(ctx, "Message must be from this server.")
                return None, None

            channel = self.bot.get_channel(channel_id)
            if not channel:
                await embeds.deny(ctx, "Channel not found.")
                return None, None

            message = await channel.fetch_message(message_id)
            return message, channel

        except:
            await embeds.deny(ctx, "Invalid message link or message not found.")
            return None, None

async def setup(bot):
    await bot.add_cog(ReactionRole(bot))
