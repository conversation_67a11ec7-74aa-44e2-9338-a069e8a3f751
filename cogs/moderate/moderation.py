import discord
from discord.ext import commands
from datetime import datetime, timedelta
import re
from utils.permissions import Per<PERSON><PERSON><PERSON><PERSON>, PermissionError<PERSON>andler
from embeds.prebuilt import embeds
from utils.finder import <PERSON><PERSON><PERSON>ber, FindUser
from utils.validators import Validators, ValidationError
from utils.constants import (
    MIN_TIMEOUT_SECONDS, MAX_TIMEOUT_SECONDS, DEFAULT_TIMEOUT_SECONDS,
    TIME_UNITS, MAX_REASON_LENGTH
)
from utils.logger import moderation_action, error, warning

class Moderation(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    def parse_duration(self, duration_str: str) -> int:
        """
        Parse duration string into seconds.
        Supports formats like: 1h, 30m, 1d, 2h30m, etc.

        Returns:
            int: Duration in seconds
        """
        if not duration_str:
            return DEFAULT_TIMEOUT_SECONDS

        # Pattern to match number + unit combinations
        pattern = r'(\d+)\s*([a-zA-Z]+)'
        matches = re.findall(pattern, duration_str.lower())

        if not matches:
            # Try to parse as just a number (assume minutes)
            try:
                return max(int(duration_str) * 60, MIN_TIMEOUT_SECONDS)
            except ValueError:
                return DEFAULT_TIMEOUT_SECONDS

        total_seconds = 0
        for amount_str, unit in matches:
            try:
                amount = int(amount_str)
                if unit in TIME_UNITS:
                    total_seconds += amount * TIME_UNITS[unit]
            except ValueError:
                continue

        # Ensure within valid range
        total_seconds = max(total_seconds, MIN_TIMEOUT_SECONDS)
        total_seconds = min(total_seconds, MAX_TIMEOUT_SECONDS)

        return total_seconds

    def format_duration(self, seconds: int) -> str:
        """
        Format duration in seconds to human readable string.

        Args:
            seconds: Duration in seconds

        Returns:
            str: Formatted duration string
        """
        if seconds < 60:
            return f"{seconds}s"
        elif seconds < 3600:
            minutes = seconds // 60
            remaining_seconds = seconds % 60
            if remaining_seconds:
                return f"{minutes}m {remaining_seconds}s"
            return f"{minutes}m"
        elif seconds < 86400:
            hours = seconds // 3600
            remaining_minutes = (seconds % 3600) // 60
            if remaining_minutes:
                return f"{hours}h {remaining_minutes}m"
            return f"{hours}h"
        else:
            days = seconds // 86400
            remaining_hours = (seconds % 86400) // 3600
            if remaining_hours:
                return f"{days}d {remaining_hours}h"
            return f"{days}d"

    async def cog_command_error(self, ctx, error):
        if isinstance(error, commands.CheckFailure):
            await PermissionErrorHandler(ctx, "permission")

    async def check_moderation_permissions(self, ctx: commands.Context, target: discord.Member) -> bool:
        """Check if user can moderate target and bot has permissions"""
        # Check if user can moderate target (role hierarchy)
        if target.top_role >= ctx.author.top_role and not await self.bot.is_owner(ctx.author):
            await embeds.deny(ctx, "You cannot moderate a user with a higher or equal role.")
            return False

        # Check if bot can moderate target
        if target.top_role >= ctx.guild.me.top_role:
            await embeds.deny(ctx, "I cannot moderate a user with a higher or equal role than mine.")
            return False

        # Don't allow self-moderation
        if target == ctx.author:
            await embeds.deny(ctx, "You cannot moderate yourself.")
            return False

        return True

    @commands.command(
        name="mute",
        description="Timeout a member for a specified duration",
        usage="mute <member> [duration] [reason]",
        example="mute @vee 10m too awesome",
        aliases=["timeout", "shh"]
    )
    @PermissionCheck.discord_permission("ModerateMembers")
    async def mute(self, ctx, member: discord.Member, duration: str = "10m", *, reason: str = "No reason provided"):
        if not await self.check_moderation_permissions(ctx, member):
            return

        # Parse duration using our improved parser
        try:
            seconds = self.parse_duration(duration)
        except Exception as e:
            await embeds.deny(ctx, f"Invalid duration format: {duration}")
            return

        # Handle timeout
        timeout_until = datetime.now() + timedelta(seconds=seconds)

        try:
            await member.timeout(timeout_until, reason=reason)

            # Log moderation action
            moderation_action(
                "TIMEOUT",
                ctx.author.id,
                member.id,
                ctx.guild.id,
                f"{duration} - {reason}"
            )

            # Format duration for display
            duration_display = self.format_duration(seconds)
            await embeds.success(ctx, f"{member.mention} was muted for {duration_display}")
        except discord.Forbidden:
            await embeds.deny(ctx, "I don't have permission to timeout this member.")
            warning(f"Failed to timeout {member.id} in {ctx.guild.id}: Missing permissions")
        except discord.HTTPException as e:
            await embeds.deny(ctx, f"Failed to timeout member: {e}")
            error(f"HTTP error during timeout of {member.id} in {ctx.guild.id}", e)

    @commands.command(
        name="unmute",
        description="Remove timeout from a member",
        usage="unmute <member> [reason]",
        example="unmute @vee good boy",
        aliases=["untimeout"]
    )
    @PermissionCheck.discord_permission("ModerateMembers")
    async def unmute(self, ctx, member: discord.Member, *, reason: str = "No reason provided"):
        if not await self.check_moderation_permissions(ctx, member):
            return

        # Remove timeout
        await member.timeout(None, reason=reason)
        await embeds.success(ctx, f"{member.mention} was unmuted")

    @commands.command(
        name="kick",
        description="Kick a member from the server",
        usage="kick <member> [reason]",
        example="kick @vee you need a break",
        aliases=[]
    )
    @PermissionCheck.discord_permission("KickMembers")
    async def kick(self, ctx, member: discord.Member, *, reason: str = "No reason provided"):
        if not await self.check_moderation_permissions(ctx, member):
            return

        # Kick member
        await member.kick(reason=reason)
        await embeds.success(ctx, f"{member.mention} was kicked")

    @commands.command(
        name="ban",
        description="Ban a user from the server (can be outside server)",
        usage="ban <user> [reason]",
        example="ban kelly e-girl",
        aliases=[]
    )
    @PermissionCheck.discord_permission("BanMembers")
    async def ban(self, ctx, *, user_input: str):
        # Parse user input and reason
        parts = user_input.split(' ', 1)
        user_query = parts[0]
        reason = parts[1] if len(parts) > 1 else "No reason provided"

        # Find user (can be outside server)
        user = await FindUser(ctx, user_query)
        if not user:
            await embeds.deny(ctx, f"User `{user_query}` not found")
            return

        # If user is in server, check moderation permissions
        if isinstance(user, discord.Member):
            if not await self.check_moderation_permissions(ctx, user):
                return

        # Ban user
        await ctx.guild.ban(user, reason=reason)
        await embeds.success(ctx, f"{user.mention} was banned")

    @commands.command(
        name="unban",
        description="Unban a user by ID or username",
        usage="unban <user> [reason]",
        example="unban kelly Forgiven",
        aliases=[]
    )
    @PermissionCheck.discord_permission("BanMembers")
    async def unban(self, ctx, *, user_input: str):
        # Parse user input and reason
        parts = user_input.split(' ', 1)
        user_query = parts[0]
        reason = parts[1] if len(parts) > 1 else "No reason provided"

        # Try to find user
        user = await FindUser(ctx, user_query)
        if user:
            user_id = user.id
            user_mention = user.mention
        else:
            # Try as raw ID if user lookup failed
            try:
                user_id = int(user_query)
                user_mention = f"<@{user_id}>"
            except ValueError:
                await embeds.deny(ctx, f"User `{user_query}` not found")
                return

        # Unban user
        user = await ctx.guild.fetch_ban(discord.Object(id=user_id))
        await ctx.guild.unban(user.user, reason=reason)
        await embeds.success(ctx, f"User {user_mention} was unbanned")

async def setup(bot):
    await bot.add_cog(Moderation(bot))
