import discord
from discord.ext import commands
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from utils.permissions import PermissionCheck, PermissionErrorHandler
from embeds.prebuilt import embeds
from utils.finder import <PERSON><PERSON>ember, FindUser
import parsedatetime
from datetime import datetime, timedelta

class Moderation(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.cal = parsedatetime.Calendar()

    async def cog_command_error(self, ctx, error):
        if isinstance(error, commands.CheckFailure):
            await Permission<PERSON>rror<PERSON>and<PERSON>(ctx, "permission")

    async def check_moderation_permissions(self, ctx: commands.Context, target: discord.Member) -> bool:
        """Check if user can moderate target and bot has permissions"""
        # Check if user can moderate target (role hierarchy)
        if target.top_role >= ctx.author.top_role and not await self.bot.is_owner(ctx.author):
            await embeds.deny(ctx, "You cannot moderate a user with a higher or equal role.")
            return False

        # Check if bot can moderate target
        if target.top_role >= ctx.guild.me.top_role:
            await embeds.deny(ctx, "I cannot moderate a user with a higher or equal role than mine.")
            return False

        # Don't allow self-moderation
        if target == ctx.author:
            await embeds.deny(ctx, "You cannot moderate yourself.")
            return False

        return True

    @commands.command(
        name="mute",
        description="Timeout a member for a specified duration",
        usage="mute <member> [duration] [reason]",
        example="mute @vee 10m too awesome",
        aliases=["timeout", "shh"]
    )
    @PermissionCheck.discord_permission("ModerateMembers")
    async def mute(self, ctx, member: discord.Member, duration: str = "10m", *, reason: str = "No reason provided"):
        if not await self.check_moderation_permissions(ctx, member):
            return

        # Parse duration using parsedatetime
        time_struct, parse_status = self.cal.parse(duration)
        if parse_status:
            from datetime import datetime
            parsed_time = datetime(*time_struct[:6])
            now = datetime.now()
            delta = parsed_time - now
            seconds = max(int(delta.total_seconds()), 60)  # Minimum 1 minute
        else:
            seconds = 600  # Default 10 minutes

        seconds = min(seconds, 2419200)  # Cap at 28 days

        # Handle timeout
        timeout_until = datetime.now() + timedelta(seconds=seconds)
        await member.timeout(timeout_until, reason=reason)
        await embeds.success(ctx, f"{member.mention} was muted for {duration}")

    @commands.command(
        name="unmute",
        description="Remove timeout from a member",
        usage="unmute <member> [reason]",
        example="unmute @vee good boy",
        aliases=["untimeout"]
    )
    @PermissionCheck.discord_permission("ModerateMembers")
    async def unmute(self, ctx, member: discord.Member, *, reason: str = "No reason provided"):
        if not await self.check_moderation_permissions(ctx, member):
            return

        # Remove timeout
        await member.timeout(None, reason=reason)
        await embeds.success(ctx, f"{member.mention} was unmuted")

    @commands.command(
        name="kick",
        description="Kick a member from the server",
        usage="kick <member> [reason]",
        example="kick @vee you need a break",
        aliases=[]
    )
    @PermissionCheck.discord_permission("KickMembers")
    async def kick(self, ctx, member: discord.Member, *, reason: str = "No reason provided"):
        if not await self.check_moderation_permissions(ctx, member):
            return

        # Kick member
        await member.kick(reason=reason)
        await embeds.success(ctx, f"{member.mention} was kicked")

    @commands.command(
        name="ban",
        description="Ban a user from the server (can be outside server)",
        usage="ban <user> [reason]",
        example="ban kelly e-girl",
        aliases=[]
    )
    @PermissionCheck.discord_permission("BanMembers")
    async def ban(self, ctx, *, user_input: str):
        # Parse user input and reason
        parts = user_input.split(' ', 1)
        user_query = parts[0]
        reason = parts[1] if len(parts) > 1 else "No reason provided"

        # Find user (can be outside server)
        user = await FindUser(ctx, user_query)
        if not user:
            await embeds.deny(ctx, f"User `{user_query}` not found")
            return

        # If user is in server, check moderation permissions
        if isinstance(user, discord.Member):
            if not await self.check_moderation_permissions(ctx, user):
                return

        # Ban user
        await ctx.guild.ban(user, reason=reason)
        await embeds.success(ctx, f"{user.mention} was banned")

    @commands.command(
        name="unban",
        description="Unban a user by ID or username",
        usage="unban <user> [reason]",
        example="unban kelly Forgiven",
        aliases=[]
    )
    @PermissionCheck.discord_permission("BanMembers")
    async def unban(self, ctx, *, user_input: str):
        # Parse user input and reason
        parts = user_input.split(' ', 1)
        user_query = parts[0]
        reason = parts[1] if len(parts) > 1 else "No reason provided"

        # Try to find user
        user = await FindUser(ctx, user_query)
        if user:
            user_id = user.id
            user_mention = user.mention
        else:
            # Try as raw ID if user lookup failed
            try:
                user_id = int(user_query)
                user_mention = f"<@{user_id}>"
            except ValueError:
                await embeds.deny(ctx, f"User `{user_query}` not found")
                return

        # Unban user
        user = await ctx.guild.fetch_ban(discord.Object(id=user_id))
        await ctx.guild.unban(user.user, reason=reason)
        await embeds.success(ctx, f"User {user_mention} was unbanned")

async def setup(bot):
    await bot.add_cog(Moderation(bot))
