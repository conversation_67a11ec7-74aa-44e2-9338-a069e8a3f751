import discord
from discord.ext import commands
from datetime import datetime, timedelta
from pytimeparse import parse

# Import global utilities
from utils import permissions, finder
from embeds.prebuilt import embeds

class Moderation(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    @commands.command(
        name="mute",
        description="Timeout a member for a specified duration",
        usage="mute <member> [duration] [reason]",
        example="mute @vee 10m too awesome",
        aliases=["timeout", "shh"]
    )
    @permissions.PermissionCheck.discord_permission("ModerateMembers")
    async def mute(self, ctx, member: discord.Member, duration: str = "10m", *, reason: str = "No reason provided"):
        # Check moderation permissions
        if not await permissions.CheckModerationPermissions(ctx, member, "mute"):
            return

        # Parse and format duration (2 lines)
        seconds = max(min(parse(duration) or 600, 2419200), 60)  # Parse duration, clamp 1min-28days
        duration_text = "28 days" if seconds >= 2419200 else f"{seconds//60} minutes" if seconds < 3600 else f"{seconds//3600} hours" if seconds < 86400 else f"{seconds//86400} days"

        # Handle timeout
        timeout_until = datetime.now() + timedelta(seconds=seconds)

        try:
            await member.timeout(timeout_until, reason=reason)
            await embeds.success(ctx, f"{member.mention} was muted for {duration_text}")
        except discord.Forbidden:
            await permissions.HandleDiscordPermissionError(ctx, "mute")
        except discord.HTTPException as e:
            await permissions.HandleDiscordError(ctx, "mute", str(e))

    @commands.command(
        name="unmute",
        description="Remove timeout from a member",
        usage="unmute <member> [reason]",
        example="unmute @vee good boy",
        aliases=["untimeout"]
    )
    @permissions.PermissionCheck.discord_permission("ModerateMembers")
    async def unmute(self, ctx, member: discord.Member, *, reason: str = "No reason provided"):
        # Check moderation permissions
        if not await permissions.CheckModerationPermissions(ctx, member, "unmute"):
            return

        try:
            await member.timeout(None, reason=reason)
            await embeds.success(ctx, f"{member.mention} was unmuted")
        except discord.Forbidden:
            await permissions.HandleDiscordPermissionError(ctx, "unmute")
        except discord.HTTPException as e:
            await permissions.HandleDiscordError(ctx, "unmute", str(e))

    @commands.command(
        name="kick",
        description="Kick a member from the server",
        usage="kick <member> [reason]",
        example="kick @vee you need a break",
        aliases=[]
    )
    @permissions.PermissionCheck.discord_permission("KickMembers")
    async def kick(self, ctx, member: discord.Member, *, reason: str = "No reason provided"):
        # Check moderation permissions
        if not await permissions.CheckModerationPermissions(ctx, member, "kick"):
            return

        try:
            await member.kick(reason=reason)
            await embeds.success(ctx, f"{member.mention} was kicked")
        except discord.Forbidden:
            await permissions.HandleDiscordPermissionError(ctx, "kick")
        except discord.HTTPException as e:
            await permissions.HandleDiscordError(ctx, "kick", str(e))

    @commands.command(
        name="ban",
        description="Ban a user from the server (can be outside server)",
        usage="ban <user> [reason]",
        example="ban kelly e-girl",
        aliases=[]
    )
    @permissions.PermissionCheck.discord_permission("BanMembers")
    async def ban(self, ctx, *, user_input: str):
        # Parse user input and reason
        parts = user_input.split(' ', 1)
        user_query = parts[0]
        reason = parts[1] if len(parts) > 1 else "No reason provided"

        # Find user (can be outside server) with automatic error handling
        user = await finder.FindUserWithError(ctx, user_query)
        if not user:
            return

        # If user is in server, check moderation permissions
        if isinstance(user, discord.Member):
            if not await permissions.CheckModerationPermissions(ctx, user, "ban"):
                return

        try:
            await ctx.guild.ban(user, reason=reason)
            await embeds.success(ctx, f"{user.mention} was banned")
        except discord.Forbidden:
            await permissions.HandleDiscordPermissionError(ctx, "ban")
        except discord.HTTPException as e:
            await permissions.HandleDiscordError(ctx, "ban", str(e))

    @commands.command(
        name="unban",
        description="Unban a user by ID or username",
        usage="unban <user> [reason]",
        example="unban kelly Forgiven",
        aliases=[]
    )
    @permissions.PermissionCheck.discord_permission("BanMembers")
    async def unban(self, ctx, *, user_input: str):
        # Parse user input and reason
        parts = user_input.split(' ', 1)
        user_query = parts[0]
        reason = parts[1] if len(parts) > 1 else "No reason provided"

        # Try to find user (don't send error automatically for unban)
        user = await finder.FindUser(ctx, user_query)
        if user:
            user_id = user.id
            user_mention = user.mention
        else:
            # Try as raw ID if user lookup failed
            try:
                user_id = int(user_query)
                user_mention = f"<@{user_id}>"
            except ValueError:
                await embeds.deny(ctx, f"User `{user_query}` not found")
                return

        try:
            # Unban user
            ban_entry = await ctx.guild.fetch_ban(discord.Object(id=user_id))
            await ctx.guild.unban(ban_entry.user, reason=reason)
            await embeds.success(ctx, f"User {user_mention} was unbanned")
        except discord.NotFound:
            await embeds.deny(ctx, f"User `{user_query}` is not banned")
        except discord.Forbidden:
            await permissions.HandleDiscordPermissionError(ctx, "unban")
        except discord.HTTPException as e:
            await permissions.HandleDiscordError(ctx, "unban", str(e))

async def setup(bot):
    await bot.add_cog(Moderation(bot))
