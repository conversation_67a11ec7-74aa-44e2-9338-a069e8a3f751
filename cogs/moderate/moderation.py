import discord
from discord.ext import commands
from datetime import datetime, timedelta
import re
import parsedatetime

# Import global utilities
from utils import permissions, finder
from embeds.prebuilt import embeds

class Moderation(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.cal = parsedatetime.Calendar()

    def parse_duration(self, duration_str: str) -> int:
        """Parse duration string and return seconds using parsedatetime library"""
        if not duration_str:
            return 600  # Default 10 minutes

        try:
            # Use parsedatetime for natural language parsing
            time_struct, parse_status = self.cal.parse(duration_str)

            if parse_status > 0:  # Successfully parsed
                parsed_time = datetime(*time_struct[:6])
                now = datetime.now()
                duration_seconds = int((parsed_time - now).total_seconds())

                # If negative or too small, try simple pattern matching
                if duration_seconds <= 0:
                    duration_seconds = self._parse_simple_duration(duration_str)

                # Ensure within valid range (1 minute to 28 days)
                duration_seconds = max(duration_seconds, 60)
                duration_seconds = min(duration_seconds, 2419200)  # 28 days
                return duration_seconds
            else:
                # Fallback to simple parsing
                return self._parse_simple_duration(duration_str)

        except Exception:
            # Fallback to simple parsing
            return self._parse_simple_duration(duration_str)

    def _parse_simple_duration(self, duration_str: str) -> int:
        """Simple duration parsing for formats like 1s, 5m, 2h, 1d, 1w"""
        # Simple regex for common patterns
        patterns = {
            r'(\d+)s': 1,      # seconds
            r'(\d+)m': 60,     # minutes
            r'(\d+)h': 3600,   # hours
            r'(\d+)d': 86400,  # days
            r'(\d+)w': 604800  # weeks
        }

        total_seconds = 0
        for pattern, multiplier in patterns.items():
            matches = re.findall(pattern, duration_str.lower())
            for match in matches:
                total_seconds += int(match) * multiplier

        if total_seconds == 0:
            # Try to parse as just a number (assume minutes)
            try:
                total_seconds = max(int(duration_str) * 60, 60)
            except ValueError:
                total_seconds = 600  # Default 10 minutes

        return total_seconds

    def format_duration(self, seconds: int) -> str:
        """
        Format duration in seconds to human readable string.

        Args:
            seconds: Duration in seconds

        Returns:
            str: Formatted duration string
        """
        if seconds < 60:
            return f"{seconds}s"
        elif seconds < 3600:
            minutes = seconds // 60
            remaining_seconds = seconds % 60
            if remaining_seconds:
                return f"{minutes}m {remaining_seconds}s"
            return f"{minutes}m"
        elif seconds < 86400:
            hours = seconds // 3600
            remaining_minutes = (seconds % 3600) // 60
            if remaining_minutes:
                return f"{hours}h {remaining_minutes}m"
            return f"{hours}h"
        else:
            days = seconds // 86400
            remaining_hours = (seconds % 86400) // 3600
            if remaining_hours:
                return f"{days}d {remaining_hours}h"
            return f"{days}d"

    async def cog_command_error(self, ctx, error):
        if isinstance(error, commands.CheckFailure):
            await permissions.HandlePermissionError(ctx, "permission")



    @commands.command(
        name="mute",
        description="Timeout a member for a specified duration",
        usage="mute <member> [duration] [reason]",
        example="mute @vee 10m too awesome",
        aliases=["timeout", "shh"]
    )
    @permissions.PermissionCheck.discord_permission("ModerateMembers")
    async def mute(self, ctx, member: discord.Member = None, duration: str = "10m", *, reason: str = "No reason provided"):
        # Check if member is provided
        if member is None:
            help_cog = self.bot.get_cog('Help')
            if help_cog:
                await help_cog.show_command_help(ctx, ctx.command)
            return

        # Check moderation permissions
        if not await permissions.CheckModerationPermissions(ctx, member, "mute"):
            return

        # Parse duration using our improved parser
        try:
            seconds = self.parse_duration(duration)
        except Exception as e:
            await embeds.deny(ctx, f"Invalid duration format: {duration}")
            return

        # Handle timeout
        timeout_until = datetime.now() + timedelta(seconds=seconds)

        try:
            await member.timeout(timeout_until, reason=reason)

            # Log moderation action (simplified)
            print(f"MODERATION: {ctx.author} timed out {member} for {duration} in {ctx.guild.name}")

            # Format duration for display
            duration_display = self.format_duration(seconds)
            await embeds.success(ctx, f"{member.mention} was muted for {duration_display}")
        except discord.Forbidden:
            await embeds.deny(ctx, "I don't have permission to timeout this member.")
        except discord.HTTPException as e:
            await embeds.deny(ctx, f"Failed to timeout member: {e}")

    @commands.command(
        name="unmute",
        description="Remove timeout from a member",
        usage="unmute <member> [reason]",
        example="unmute @vee good boy",
        aliases=["untimeout"]
    )
    @permissions.PermissionCheck.discord_permission("ModerateMembers")
    async def unmute(self, ctx, member: discord.Member = None, *, reason: str = "No reason provided"):
        # Check if member is provided
        if member is None:
            help_cog = self.bot.get_cog('Help')
            if help_cog:
                await help_cog.show_command_help(ctx, ctx.command)
            return

        # Check moderation permissions
        if not await permissions.CheckModerationPermissions(ctx, member, "unmute"):
            return

        # Remove timeout
        await member.timeout(None, reason=reason)
        await embeds.success(ctx, f"{member.mention} was unmuted")

    @commands.command(
        name="kick",
        description="Kick a member from the server",
        usage="kick <member> [reason]",
        example="kick @vee you need a break",
        aliases=[]
    )
    @permissions.PermissionCheck.discord_permission("KickMembers")
    async def kick(self, ctx, member: discord.Member = None, *, reason: str = "No reason provided"):
        # Check if member is provided
        if member is None:
            help_cog = self.bot.get_cog('Help')
            if help_cog:
                await help_cog.show_command_help(ctx, ctx.command)
            return

        # Check moderation permissions
        if not await permissions.CheckModerationPermissions(ctx, member, "kick"):
            return

        # Kick member
        await member.kick(reason=reason)
        await embeds.success(ctx, f"{member.mention} was kicked")

    @commands.command(
        name="ban",
        description="Ban a user from the server (can be outside server)",
        usage="ban <user> [reason]",
        example="ban kelly e-girl",
        aliases=[]
    )
    @permissions.PermissionCheck.discord_permission("BanMembers")
    async def ban(self, ctx, *, user_input: str = None):
        # Check if user input is provided
        if user_input is None:
            help_cog = self.bot.get_cog('Help')
            if help_cog:
                await help_cog.show_command_help(ctx, ctx.command)
            return

        # Parse user input and reason
        parts = user_input.split(' ', 1)
        user_query = parts[0]
        reason = parts[1] if len(parts) > 1 else "No reason provided"

        # Find user (can be outside server)
        user = await finder.FindUser(ctx, user_query)
        if not user:
            await embeds.deny(ctx, f"User `{user_query}` not found")
            return

        # If user is in server, check moderation permissions
        if isinstance(user, discord.Member):
            if not await permissions.CheckModerationPermissions(ctx, user, "ban"):
                return

        # Ban user
        await ctx.guild.ban(user, reason=reason)
        await embeds.success(ctx, f"{user.mention} was banned")

    @commands.command(
        name="unban",
        description="Unban a user by ID or username",
        usage="unban <user> [reason]",
        example="unban kelly Forgiven",
        aliases=[]
    )
    @permissions.PermissionCheck.discord_permission("BanMembers")
    async def unban(self, ctx, *, user_input: str = None):
        # Check if user input is provided
        if user_input is None:
            help_cog = self.bot.get_cog('Help')
            if help_cog:
                await help_cog.show_command_help(ctx, ctx.command)
            return

        # Parse user input and reason
        parts = user_input.split(' ', 1)
        user_query = parts[0]
        reason = parts[1] if len(parts) > 1 else "No reason provided"

        # Try to find user
        user = await finder.FindUser(ctx, user_query)
        if user:
            user_id = user.id
            user_mention = user.mention
        else:
            # Try as raw ID if user lookup failed
            try:
                user_id = int(user_query)
                user_mention = f"<@{user_id}>"
            except ValueError:
                await embeds.deny(ctx, f"User `{user_query}` not found")
                return

        # Unban user
        user = await ctx.guild.fetch_ban(discord.Object(id=user_id))
        await ctx.guild.unban(user.user, reason=reason)
        await embeds.success(ctx, f"User {user_mention} was unbanned")

async def setup(bot):
    await bot.add_cog(Moderation(bot))
