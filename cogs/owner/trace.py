import discord
from discord.ext import commands
import random
import string
import traceback
from datetime import datetime, timedelta
from typing import Dict, Optional
import asyncio

# Import global utilities
from utils import permissions, config
from embeds.prebuilt import embeds

class ErrorTracker:
    """Global error tracking system for the bot"""

    def __init__(self):
        self.errors: Dict[str, dict] = {}
        self.max_errors = 1000  # Maximum number of errors to keep
        self.error_ttl = timedelta(hours=24)  # Errors expire after 24 hours
        self._cleanup_task = None
    
    def generate_error_code(self) -> str:
        """Generate a unique error code"""
        return ''.join(random.choices(string.ascii_letters + string.digits, k=16))
    
    def log_error(self, ctx: commands.Context, error: Exception, command_name: str = None) -> str:
        """Log an error and return the error code"""
        error_code = self.generate_error_code()
        
        # Get command name
        if not command_name:
            command_name = ctx.command.qualified_name if ctx.command else "Unknown"
        
        # Get full traceback
        error_traceback = ''.join(traceback.format_exception(type(error), error, error.__traceback__))
        
        # Store error details
        self.errors[error_code] = {
            'timestamp': datetime.now(),
            'user_id': ctx.author.id,
            'user_name': ctx.author.name,
            'guild_id': ctx.guild.id if ctx.guild else None,
            'guild_name': ctx.guild.name if ctx.guild else "DM",
            'channel_id': ctx.channel.id,
            'channel_name': getattr(ctx.channel, 'name', 'DM'),
            'command': command_name,
            'message_content': ctx.message.content,
            'error_type': type(error).__name__,
            'error_message': str(error),
            'traceback': error_traceback
        }
        
        # Start cleanup task if not already running
        if self._cleanup_task is None:
            self._cleanup_task = asyncio.create_task(self._cleanup_loop())

        return error_code

    def get_error(self, error_code: str) -> Optional[dict]:
        """Get error details by code"""
        error_data = self.errors.get(error_code)

        # Check if error has expired
        if error_data and datetime.now() - error_data['timestamp'] > self.error_ttl:
            del self.errors[error_code]
            return None

        return error_data

    async def _cleanup_loop(self):
        """Background task to clean up expired errors"""
        while True:
            try:
                await asyncio.sleep(3600)  # Run every hour
                await self._cleanup_expired_errors()
            except asyncio.CancelledError:
                break
            except Exception as e:
                print(f"Error in cleanup loop: {e}")

    async def _cleanup_expired_errors(self):
        """Remove expired errors and enforce max limit"""
        now = datetime.now()
        expired_codes = []

        # Find expired errors
        for code, error_data in self.errors.items():
            if now - error_data['timestamp'] > self.error_ttl:
                expired_codes.append(code)

        # Remove expired errors
        for code in expired_codes:
            del self.errors[code]

        # If still over limit, remove oldest errors
        if len(self.errors) > self.max_errors:
            sorted_errors = sorted(
                self.errors.items(),
                key=lambda x: x[1]['timestamp']
            )

            # Keep only the most recent max_errors
            errors_to_remove = len(self.errors) - self.max_errors
            for i in range(errors_to_remove):
                code = sorted_errors[i][0]
                del self.errors[code]

        print(f"Error cleanup completed. {len(expired_codes)} expired errors removed. "
              f"Total errors: {len(self.errors)}")

    def stop_cleanup(self):
        """Stop the cleanup task"""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            self._cleanup_task = None

# Global error tracker instance
error_tracker = ErrorTracker()

async def handle_command_error(ctx: commands.Context, error: Exception, command_name: str = None) -> str:
    """Handle command errors and return error code"""
    # Log the error and get code
    error_code = error_tracker.log_error(ctx, error, command_name)
    
    # Send user-friendly error message
    await embeds.warn(
        ctx, 
        f"{ctx.author.mention}: Error occurred while performing command **{command_name or 'unknown'}**. "
        f"Please report the error code to the **developer in [official server]({config.discord_server})**"
    )
    
    # Send error code outside embed
    await ctx.send(f"`{error_code}`")
    
    return error_code

class Trace(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    async def cog_command_error(self, ctx, error):
        if isinstance(error, commands.CheckFailure):
            await embeds.deny(ctx, "This command is owner-only")

    @commands.command(
        name="trace",
        description="Get detailed error information by error code",
        usage="trace <error_code>",
        example="trace Z6oWNqQYjLWN8"
    )
    @permissions.PermissionCheck.owner_only()
    async def trace(self, ctx, error_code: str = None):
        if not error_code:
            help_cog = self.bot.get_cog('Help')
            if help_cog:
                await help_cog.show_command_help(ctx, ctx.command)
            return

        # Get error details
        error_data = error_tracker.get_error(error_code)
        if not error_data:
            await embeds.deny(ctx, f"Error code `{error_code}` not found or has expired.")
            return

        # Create detailed error embed
        from embeds.parse import create_embed
        embed = create_embed(
            title=f"Error Code {error_code}",
            description=f"`{error_data['error_type']}: {error_data['error_message']}`",
            color=config.colors['error']
        )

        # Add context information
        context_info = []
        context_info.append(f"**User:** {error_data['user_name']} (`{error_data['user_id']}`)")
        context_info.append(f"**Guild:** {error_data['guild_name']} (`{error_data['guild_id']}`)")
        context_info.append(f"**Channel:** #{error_data['channel_name']} (`{error_data['channel_id']}`)")
        context_info.append(f"**Command:** `{error_data['command']}`")
        context_info.append(f"**Message:** `{error_data['message_content']}`")
        context_info.append(f"**Time:** {error_data['timestamp'].strftime('%Y-%m-%d %H:%M:%S UTC')}")

        embed.add_field(
            name="Context",
            value="\n".join(context_info),
            inline=False
        )

        # Add traceback (truncated if too long)
        traceback_text = error_data['traceback']
        if len(traceback_text) > 1000:
            traceback_text = traceback_text[-1000:] + "\n... (truncated)"

        embed.add_field(
            name="Traceback",
            value=f"```python\n{traceback_text}\n```",
            inline=False
        )

        await ctx.send(embed=embed)

async def setup(bot):
    await bot.add_cog(Trace(bot))
