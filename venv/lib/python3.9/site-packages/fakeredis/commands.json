{"acl cat": ["acl|cat", -2, ["noscript", "loading", "stale"], 0, 0, 0, ["@slow"], [], [], []], "acl": ["acl", -1, [], 0, 0, 0, [], [], [], [["acl|cat", -2, ["noscript", "loading", "stale"], 0, 0, 0, ["@slow"], [], [], []], ["acl|deluser", -3, ["admin", "noscript", "loading", "stale"], 0, 0, 0, ["@admin", "@slow", "@dangerous"], [], [], []], ["acl|genpass", -2, ["noscript", "loading", "stale"], 0, 0, 0, ["@slow"], [], [], []], ["acl|getuser", 3, ["admin", "noscript", "loading", "stale"], 0, 0, 0, ["@admin", "@slow", "@dangerous"], [], [], []], ["acl|list", 2, ["admin", "noscript", "loading", "stale"], 0, 0, 0, ["@admin", "@slow", "@dangerous"], [], [], []], ["acl|load", 2, ["admin", "noscript", "loading", "stale"], 0, 0, 0, ["@admin", "@slow", "@dangerous"], [], [], []], ["acl|log", -2, ["admin", "noscript", "loading", "stale"], 0, 0, 0, ["@admin", "@slow", "@dangerous"], [], [], []], ["acl|save", 2, ["admin", "noscript", "loading", "stale"], 0, 0, 0, ["@admin", "@slow", "@dangerous"], [], [], []], ["acl|setuser", -3, ["admin", "noscript", "loading", "stale"], 0, 0, 0, ["@admin", "@slow", "@dangerous"], [], [], []], ["acl|users", 2, ["admin", "noscript", "loading", "stale"], 0, 0, 0, ["@admin", "@slow", "@dangerous"], [], [], []], ["acl|whoami", 2, ["noscript", "loading", "stale"], 0, 0, 0, ["@slow"], [], [], []]]], "acl deluser": ["acl|deluser", -3, ["admin", "noscript", "loading", "stale"], 0, 0, 0, ["@admin", "@slow", "@dangerous"], [], [], []], "acl genpass": ["acl|genpass", -2, ["noscript", "loading", "stale"], 0, 0, 0, ["@slow"], [], [], []], "acl getuser": ["acl|getuser", 3, ["admin", "noscript", "loading", "stale"], 0, 0, 0, ["@admin", "@slow", "@dangerous"], [], [], []], "acl list": ["acl|list", 2, ["admin", "noscript", "loading", "stale"], 0, 0, 0, ["@admin", "@slow", "@dangerous"], [], [], []], "acl load": ["acl|load", 2, ["admin", "noscript", "loading", "stale"], 0, 0, 0, ["@admin", "@slow", "@dangerous"], [], [], []], "acl log": ["acl|log", -2, ["admin", "noscript", "loading", "stale"], 0, 0, 0, ["@admin", "@slow", "@dangerous"], [], [], []], "acl save": ["acl|save", 2, ["admin", "noscript", "loading", "stale"], 0, 0, 0, ["@admin", "@slow", "@dangerous"], [], [], []], "acl setuser": ["acl|setuser", -3, ["admin", "noscript", "loading", "stale"], 0, 0, 0, ["@admin", "@slow", "@dangerous"], [], [], []], "acl users": ["acl|users", 2, ["admin", "noscript", "loading", "stale"], 0, 0, 0, ["@admin", "@slow", "@dangerous"], [], [], []], "acl whoami": ["acl|whoami", 2, ["noscript", "loading", "stale"], 0, 0, 0, ["@slow"], [], [], []], "append": ["append", 3, ["write", "denyoom", "fast"], 1, 1, 1, ["@fast", "@string", "@write"], [], [], []], "auth": ["auth", -2, ["noscript", "loading", "stale", "fast", "no_auth", "allow_busy"], 0, 0, 0, ["@fast", "@connection"], [], [], []], "bgsave": ["bgsave", -1, ["admin", "noscript", "no_async_loading"], 0, 0, 0, ["@admin", "@slow", "@dangerous"], [], [], []], "bitcount": ["bitcount", -2, ["readonly"], 1, 1, 1, ["@read", "@slow", "@bitmap"], [], [], []], "bitfield": ["bitfield", -2, ["write", "denyoom"], 1, 1, 1, ["@bitmap", "@slow", "@write"], [], [], [["bitfield_ro", -2, ["readonly", "fast"], 1, 1, 1, ["@fast", "@read", "@bitmap"], [], [], []]]], "bitop": ["bitop", -4, ["write", "denyoom"], 2, 3, 1, ["@bitmap", "@slow", "@write"], [], [], []], "bitpos": ["bitpos", -3, ["readonly"], 1, 1, 1, ["@read", "@slow", "@bitmap"], [], [], []], "blmove": ["blmove", 6, ["write", "denyoom", "blocking"], 1, 2, 1, ["@list", "@blocking", "@slow", "@write"], [], [], []], "blmpop": ["blmpop", -5, ["write", "blocking", "movablekeys"], 2, 2, 1, ["@list", "@blocking", "@slow", "@write"], [], [], []], "blpop": ["blpop", -3, ["write", "blocking"], 1, 1, 1, ["@list", "@blocking", "@slow", "@write"], [], [], []], "brpop": ["brpop", -3, ["write", "blocking"], 1, 1, 1, ["@list", "@blocking", "@slow", "@write"], [], [], [["brpoplpush", 4, ["write", "denyoom", "blocking"], 1, 2, 1, ["@list", "@blocking", "@slow", "@write"], [], [], []]]], "brpoplpush": ["brpoplpush", 4, ["write", "denyoom", "blocking"], 1, 2, 1, ["@list", "@blocking", "@slow", "@write"], [], [], []], "bzmpop": ["bzmpop", -5, ["write", "blocking", "movablekeys"], 2, 2, 1, ["@blocking", "@sortedset", "@slow", "@write"], [], [], []], "bzpopmax": ["bzpopmax", -3, ["write", "blocking", "fast"], 1, 1, 1, ["@blocking", "@fast", "@sortedset", "@write"], [], [], []], "bzpopmin": ["bzpopmin", -3, ["write", "blocking", "fast"], 1, 1, 1, ["@blocking", "@fast", "@sortedset", "@write"], [], [], []], "client getname": ["client|getname", 2, ["noscript", "loading", "stale"], 0, 0, 0, ["@connection", "@slow"], [], [], []], "client": ["client", -1, [], 0, 0, 0, [], [], [], [["client|getname", 2, ["noscript", "loading", "stale"], 0, 0, 0, ["@connection", "@slow"], [], [], []], ["client|id", 2, ["noscript", "loading", "stale"], 0, 0, 0, ["@connection", "@slow"], [], [], []], ["client|info", 2, ["noscript", "loading", "stale"], 0, 0, 0, ["@connection", "@slow"], [], [], []], ["client|list", -2, ["admin", "noscript", "loading", "stale"], 0, 0, 0, ["@admin", "@connection", "@slow", "@dangerous"], [], [], []], ["client|setinfo", 4, ["noscript", "loading", "stale"], 0, 0, 0, ["@connection", "@slow"], [], [], []], ["client|setname", 3, ["noscript", "loading", "stale"], 0, 0, 0, ["@connection", "@slow"], [], [], []]]], "client id": ["client|id", 2, ["noscript", "loading", "stale"], 0, 0, 0, ["@connection", "@slow"], [], [], []], "client info": ["client|info", 2, ["noscript", "loading", "stale"], 0, 0, 0, ["@connection", "@slow"], [], [], []], "client list": ["client|list", -2, ["admin", "noscript", "loading", "stale"], 0, 0, 0, ["@admin", "@connection", "@slow", "@dangerous"], [], [], []], "client setinfo": ["client|setinfo", 4, ["noscript", "loading", "stale"], 0, 0, 0, ["@connection", "@slow"], [], [], []], "client setname": ["client|setname", 3, ["noscript", "loading", "stale"], 0, 0, 0, ["@connection", "@slow"], [], [], []], "command": ["command", -1, ["loading", "stale"], 0, 0, 0, ["@connection", "@slow"], [], [], [["command|count", 2, ["loading", "stale"], 0, 0, 0, ["@connection", "@slow"], [], [], []], ["command|docs", -2, ["loading", "stale"], 0, 0, 0, ["@connection", "@slow"], [], [], []], ["command|getkeys", -3, ["loading", "stale"], 0, 0, 0, ["@connection", "@slow"], [], [], [["command|getkeysandflags", -3, ["loading", "stale"], 0, 0, 0, ["@connection", "@slow"], [], [], []]]], ["command|getkeysandflags", -3, ["loading", "stale"], 0, 0, 0, ["@connection", "@slow"], [], [], []], ["command|help", 2, ["loading", "stale"], 0, 0, 0, ["@connection", "@slow"], [], [], []], ["command|info", -2, ["loading", "stale"], 0, 0, 0, ["@connection", "@slow"], [], [], []], ["command|list", -2, ["loading", "stale"], 0, 0, 0, ["@connection", "@slow"], [], [], []], ["command|count", 2, ["loading", "stale"], 0, 0, 0, ["@connection", "@slow"], [], [], []], ["command|info", -2, ["loading", "stale"], 0, 0, 0, ["@connection", "@slow"], [], [], []]]], "command count": ["command|count", 2, ["loading", "stale"], 0, 0, 0, ["@connection", "@slow"], [], [], []], "command info": ["command|info", -2, ["loading", "stale"], 0, 0, 0, ["@connection", "@slow"], [], [], []], "config set": ["config|set", -4, ["admin", "noscript", "loading", "stale"], 0, 0, 0, ["@admin", "@slow", "@dangerous"], [], [], []], "config": ["config", -1, [], 0, 0, 0, [], [], [], [["config|set", -4, ["admin", "noscript", "loading", "stale"], 0, 0, 0, ["@admin", "@slow", "@dangerous"], [], [], []]]], "copy": ["copy", -3, ["write", "denyoom"], 1, 2, 1, ["@keyspace", "@slow", "@write"], [], [], []], "dbsize": ["dbsize", 1, ["readonly", "fast"], 0, 0, 0, ["@keyspace", "@fast", "@read"], [], [], []], "decr": ["decr", 2, ["write", "denyoom", "fast"], 1, 1, 1, ["@fast", "@string", "@write"], [], [], [["dec<PERSON>", 3, ["write", "denyoom", "fast"], 1, 1, 1, ["@fast", "@string", "@write"], [], [], []]]], "decrby": ["dec<PERSON>", 3, ["write", "denyoom", "fast"], 1, 1, 1, ["@fast", "@string", "@write"], [], [], []], "del": ["del", -2, ["write"], 1, 1, 1, ["@keyspace", "@slow", "@write"], [], [], []], "discard": ["discard", 1, ["noscript", "loading", "stale", "fast", "allow_busy"], 0, 0, 0, ["@transaction", "@fast"], [], [], []], "dump": ["dump", 2, ["readonly"], 1, 1, 1, ["@keyspace", "@read", "@slow"], [], [], []], "echo": ["echo", 2, ["loading", "stale", "fast"], 0, 0, 0, ["@fast", "@connection"], [], [], []], "eval": ["eval", -3, ["noscript", "stale", "skip_monitor", "no_mandatory_keys", "movablekeys"], 2, 2, 1, ["@scripting", "@slow"], [], [], [["e<PERSON><PERSON>", -3, ["noscript", "stale", "skip_monitor", "no_mandatory_keys", "movablekeys"], 2, 2, 1, ["@scripting", "@slow"], [], [], [["eval<PERSON>_ro", -3, ["readonly", "noscript", "stale", "skip_monitor", "no_mandatory_keys", "movablekeys"], 2, 2, 1, ["@scripting", "@slow"], [], [], []]]], ["eval<PERSON>_ro", -3, ["readonly", "noscript", "stale", "skip_monitor", "no_mandatory_keys", "movablekeys"], 2, 2, 1, ["@scripting", "@slow"], [], [], []], ["eval_ro", -3, ["readonly", "noscript", "stale", "skip_monitor", "no_mandatory_keys", "movablekeys"], 2, 2, 1, ["@scripting", "@slow"], [], [], []]]], "evalsha": ["e<PERSON><PERSON>", -3, ["noscript", "stale", "skip_monitor", "no_mandatory_keys", "movablekeys"], 2, 2, 1, ["@scripting", "@slow"], [], [], [["eval<PERSON>_ro", -3, ["readonly", "noscript", "stale", "skip_monitor", "no_mandatory_keys", "movablekeys"], 2, 2, 1, ["@scripting", "@slow"], [], [], []]]], "exec": ["exec", 1, ["noscript", "loading", "stale", "skip_slowlog"], 0, 0, 0, ["@transaction", "@slow"], [], [], []], "exists": ["exists", -2, ["readonly", "fast"], 1, 1, 1, ["@keyspace", "@fast", "@read"], [], [], []], "expire": ["expire", -3, ["write", "fast"], 1, 1, 1, ["@keyspace", "@fast", "@write"], [], [], [["expireat", -3, ["write", "fast"], 1, 1, 1, ["@keyspace", "@fast", "@write"], [], [], []], ["expiretime", 2, ["readonly", "fast"], 1, 1, 1, ["@keyspace", "@fast", "@read"], [], [], []]]], "expireat": ["expireat", -3, ["write", "fast"], 1, 1, 1, ["@keyspace", "@fast", "@write"], [], [], []], "expiretime": ["expiretime", 2, ["readonly", "fast"], 1, 1, 1, ["@keyspace", "@fast", "@read"], [], [], []], "flushall": ["flushall", -1, ["write"], 0, 0, 0, ["@keyspace", "@dangerous", "@slow", "@write"], [], [], []], "flushdb": ["flushdb", -1, ["write"], 0, 0, 0, ["@keyspace", "@dangerous", "@slow", "@write"], [], [], []], "geoadd": ["geoadd", -5, ["write", "denyoom"], 1, 1, 1, ["@geo", "@slow", "@write"], [], [], []], "geodist": ["geodist", -4, ["readonly"], 1, 1, 1, ["@read", "@slow", "@geo"], [], [], []], "geohash": ["geo<PERSON>h", -2, ["readonly"], 1, 1, 1, ["@read", "@slow", "@geo"], [], [], []], "geopos": ["geopos", -2, ["readonly"], 1, 1, 1, ["@read", "@slow", "@geo"], [], [], []], "georadius": ["georadius", -6, ["write", "denyoom", "movablekeys"], 1, 0, 1, ["@geo", "@slow", "@write"], [], [], [["georadiusbymember", -5, ["write", "denyoom", "movablekeys"], 1, 0, 1, ["@geo", "@slow", "@write"], [], [], [["georadiusbymember_ro", -5, ["readonly"], 1, 1, 1, ["@read", "@slow", "@geo"], [], [], []]]], ["georadiusbymember_ro", -5, ["readonly"], 1, 1, 1, ["@read", "@slow", "@geo"], [], [], []], ["georadius_ro", -6, ["readonly"], 1, 1, 1, ["@read", "@slow", "@geo"], [], [], []]]], "georadiusbymember": ["georadiusbymember", -5, ["write", "denyoom", "movablekeys"], 1, 0, 1, ["@geo", "@slow", "@write"], [], [], [["georadiusbymember_ro", -5, ["readonly"], 1, 1, 1, ["@read", "@slow", "@geo"], [], [], []]]], "georadiusbymember_ro": ["georadiusbymember_ro", -5, ["readonly"], 1, 1, 1, ["@read", "@slow", "@geo"], [], [], []], "georadius_ro": ["georadius_ro", -6, ["readonly"], 1, 1, 1, ["@read", "@slow", "@geo"], [], [], []], "geosearch": ["geosearch", -7, ["readonly"], 1, 1, 1, ["@read", "@slow", "@geo"], [], [], [["geosearchstore", -8, ["write", "denyoom"], 1, 2, 1, ["@geo", "@slow", "@write"], [], [], []]]], "geosearchstore": ["geosearchstore", -8, ["write", "denyoom"], 1, 2, 1, ["@geo", "@slow", "@write"], [], [], []], "get": ["get", 2, ["readonly", "fast"], 1, 1, 1, ["@fast", "@read", "@string"], [], [], [["getbit", 3, ["readonly", "fast"], 1, 1, 1, ["@fast", "@read", "@bitmap"], [], [], []], ["getdel", 2, ["write", "fast"], 1, 1, 1, ["@fast", "@string", "@write"], [], [], []], ["getex", -2, ["write", "fast"], 1, 1, 1, ["@fast", "@string", "@write"], [], [], []], ["getrange", 4, ["readonly"], 1, 1, 1, ["@read", "@string", "@slow"], [], [], []], ["getset", 3, ["write", "denyoom", "fast"], 1, 1, 1, ["@fast", "@string", "@write"], [], [], []]]], "getbit": ["getbit", 3, ["readonly", "fast"], 1, 1, 1, ["@fast", "@read", "@bitmap"], [], [], []], "getdel": ["getdel", 2, ["write", "fast"], 1, 1, 1, ["@fast", "@string", "@write"], [], [], []], "getex": ["getex", -2, ["write", "fast"], 1, 1, 1, ["@fast", "@string", "@write"], [], [], []], "getrange": ["getrange", 4, ["readonly"], 1, 1, 1, ["@read", "@string", "@slow"], [], [], []], "getset": ["getset", 3, ["write", "denyoom", "fast"], 1, 1, 1, ["@fast", "@string", "@write"], [], [], []], "hdel": ["hdel", -3, ["write", "fast"], 1, 1, 1, ["@hash", "@fast", "@write"], [], [], []], "hello": ["hello", -1, ["noscript", "loading", "stale", "fast", "no_auth", "allow_busy"], 0, 0, 0, ["@fast", "@connection"], [], [], []], "hexists": ["hexists", 3, ["readonly", "fast"], 1, 1, 1, ["@hash", "@fast", "@read"], [], [], []], "hexpire": ["hexpire", -5, ["write", "denyoom", "fast"], 1, 1, 1, ["@hash", "@fast", "@write"], [], [], [["hexpireat", -5, ["write", "denyoom", "fast"], 1, 1, 1, ["@hash", "@fast", "@write"], [], [], []], ["hexpiretime", -4, ["readonly", "fast"], 1, 1, 1, ["@hash", "@fast", "@read"], [], [], []]]], "hexpireat": ["hexpireat", -5, ["write", "denyoom", "fast"], 1, 1, 1, ["@hash", "@fast", "@write"], [], [], []], "hexpiretime": ["hexpiretime", -4, ["readonly", "fast"], 1, 1, 1, ["@hash", "@fast", "@read"], [], [], []], "hget": ["hget", 3, ["readonly", "fast"], 1, 1, 1, ["@hash", "@fast", "@read"], [], [], [["hgetall", 2, ["readonly"], 1, 1, 1, ["@hash", "@read", "@slow"], [], [], []], ["hgetf", -5, ["write", "denyoom", "fast"], 1, 1, 1, ["@hash", "@fast", "@write"], [], [], []]]], "hgetall": ["hgetall", 2, ["readonly"], 1, 1, 1, ["@hash", "@read", "@slow"], [], [], []], "hincrby": ["hinc<PERSON>", 4, ["write", "denyoom", "fast"], 1, 1, 1, ["@hash", "@fast", "@write"], [], [], [["hincrbyfloat", 4, ["write", "denyoom", "fast"], 1, 1, 1, ["@hash", "@fast", "@write"], [], [], []]]], "hincrbyfloat": ["hincrbyfloat", 4, ["write", "denyoom", "fast"], 1, 1, 1, ["@hash", "@fast", "@write"], [], [], []], "hkeys": ["hkeys", 2, ["readonly"], 1, 1, 1, ["@hash", "@read", "@slow"], [], [], []], "hlen": ["hlen", 2, ["readonly", "fast"], 1, 1, 1, ["@hash", "@fast", "@read"], [], [], []], "hmget": ["hmget", -3, ["readonly", "fast"], 1, 1, 1, ["@hash", "@fast", "@read"], [], [], []], "hmset": ["hmset", -4, ["write", "denyoom", "fast"], 1, 1, 1, ["@hash", "@fast", "@write"], [], [], []], "hpersist": ["hpersist", -4, ["readonly", "fast"], 1, 1, 1, ["@hash", "@fast", "@read"], [], [], []], "hpexpire": ["hpexpire", -5, ["write", "denyoom", "fast"], 1, 1, 1, ["@hash", "@fast", "@write"], [], [], [["hpexpireat", -5, ["write", "denyoom", "fast"], 1, 1, 1, ["@hash", "@fast", "@write"], [], [], []], ["hpexpiretime", -4, ["readonly", "fast"], 1, 1, 1, ["@hash", "@fast", "@read"], [], [], []]]], "hpexpireat": ["hpexpireat", -5, ["write", "denyoom", "fast"], 1, 1, 1, ["@hash", "@fast", "@write"], [], [], []], "hpexpiretime": ["hpexpiretime", -4, ["readonly", "fast"], 1, 1, 1, ["@hash", "@fast", "@read"], [], [], []], "hpttl": ["hpttl", -4, ["readonly", "fast"], 1, 1, 1, ["@hash", "@fast", "@read"], [], [], []], "hrandfield": ["hrand<PERSON>", -2, ["readonly"], 1, 1, 1, ["@hash", "@read", "@slow"], [], [], []], "hscan": ["hscan", -3, ["readonly"], 1, 1, 1, ["@hash", "@read", "@slow"], [], [], []], "hset": ["hset", -4, ["write", "denyoom", "fast"], 1, 1, 1, ["@hash", "@fast", "@write"], [], [], [["hsetf", -6, ["write", "denyoom", "fast"], 1, 1, 1, ["@hash", "@fast", "@write"], [], [], []], ["hsetnx", 4, ["write", "denyoom", "fast"], 1, 1, 1, ["@hash", "@fast", "@write"], [], [], []]]], "hsetnx": ["hsetnx", 4, ["write", "denyoom", "fast"], 1, 1, 1, ["@hash", "@fast", "@write"], [], [], []], "hstrlen": ["hst<PERSON>n", 3, ["readonly", "fast"], 1, 1, 1, ["@hash", "@fast", "@read"], [], [], []], "httl": ["httl", -4, ["readonly", "fast"], 1, 1, 1, ["@hash", "@fast", "@read"], [], [], []], "hvals": ["hvals", 2, ["readonly"], 1, 1, 1, ["@hash", "@read", "@slow"], [], [], []], "incr": ["incr", 2, ["write", "denyoom", "fast"], 1, 1, 1, ["@fast", "@string", "@write"], [], [], [["incrby", 3, ["write", "denyoom", "fast"], 1, 1, 1, ["@fast", "@string", "@write"], [], [], [["incrbyfloat", 3, ["write", "denyoom", "fast"], 1, 1, 1, ["@fast", "@string", "@write"], [], [], []]]], ["incrbyfloat", 3, ["write", "denyoom", "fast"], 1, 1, 1, ["@fast", "@string", "@write"], [], [], []]]], "incrby": ["incrby", 3, ["write", "denyoom", "fast"], 1, 1, 1, ["@fast", "@string", "@write"], [], [], [["incrbyfloat", 3, ["write", "denyoom", "fast"], 1, 1, 1, ["@fast", "@string", "@write"], [], [], []]]], "incrbyfloat": ["incrbyfloat", 3, ["write", "denyoom", "fast"], 1, 1, 1, ["@fast", "@string", "@write"], [], [], []], "keys": ["keys", 2, ["readonly"], 0, 0, 0, ["@keyspace", "@read", "@slow", "@dangerous"], [], [], []], "lastsave": ["lastsave", 1, ["loading", "stale", "fast"], 0, 0, 0, ["@admin", "@fast", "@dangerous"], [], [], []], "lcs": ["lcs", -3, ["readonly"], 1, 1, 1, ["@read", "@string", "@slow"], [], [], []], "lindex": ["lindex", 3, ["readonly"], 1, 1, 1, ["@list", "@read", "@slow"], [], [], []], "linsert": ["linsert", 5, ["write", "denyoom"], 1, 1, 1, ["@list", "@slow", "@write"], [], [], []], "llen": ["llen", 2, ["readonly", "fast"], 1, 1, 1, ["@list", "@fast", "@read"], [], [], []], "lmove": ["lmove", 5, ["write", "denyoom"], 1, 2, 1, ["@list", "@slow", "@write"], [], [], []], "lmpop": ["lmpop", -4, ["write", "movablekeys"], 1, 1, 1, ["@list", "@slow", "@write"], [], [], []], "lpop": ["lpop", -2, ["write", "fast"], 1, 1, 1, ["@list", "@fast", "@write"], [], [], []], "lpos": ["lpos", -3, ["readonly"], 1, 1, 1, ["@list", "@read", "@slow"], [], [], []], "lpush": ["lpush", -3, ["write", "denyoom", "fast"], 1, 1, 1, ["@list", "@fast", "@write"], [], [], [["lpushx", -3, ["write", "denyoom", "fast"], 1, 1, 1, ["@list", "@fast", "@write"], [], [], []]]], "lpushx": ["lpushx", -3, ["write", "denyoom", "fast"], 1, 1, 1, ["@list", "@fast", "@write"], [], [], []], "lrange": ["lrange", 4, ["readonly"], 1, 1, 1, ["@list", "@read", "@slow"], [], [], []], "lrem": ["lrem", 4, ["write"], 1, 1, 1, ["@list", "@slow", "@write"], [], [], []], "lset": ["lset", 4, ["write", "denyoom"], 1, 1, 1, ["@list", "@slow", "@write"], [], [], []], "ltrim": ["ltrim", 4, ["write"], 1, 1, 1, ["@list", "@slow", "@write"], [], [], []], "mget": ["mget", -2, ["readonly", "fast"], 1, 1, 1, ["@fast", "@read", "@string"], [], [], []], "move": ["move", 3, ["write", "fast"], 1, 1, 1, ["@keyspace", "@fast", "@write"], [], [], []], "mset": ["mset", -3, ["write", "denyoom"], 1, 1, 2, ["@string", "@slow", "@write"], [], [], [["msetnx", -3, ["write", "denyoom"], 1, 1, 2, ["@string", "@slow", "@write"], [], [], []]]], "msetnx": ["msetnx", -3, ["write", "denyoom"], 1, 1, 2, ["@string", "@slow", "@write"], [], [], []], "multi": ["multi", 1, ["noscript", "loading", "stale", "fast", "allow_busy"], 0, 0, 0, ["@transaction", "@fast"], [], [], []], "persist": ["persist", 2, ["write", "fast"], 1, 1, 1, ["@keyspace", "@fast", "@write"], [], [], []], "pexpire": ["pexpire", -3, ["write", "fast"], 1, 1, 1, ["@keyspace", "@fast", "@write"], [], [], [["pexpireat", -3, ["write", "fast"], 1, 1, 1, ["@keyspace", "@fast", "@write"], [], [], []], ["pexpiretime", 2, ["readonly", "fast"], 1, 1, 1, ["@keyspace", "@fast", "@read"], [], [], []]]], "pexpireat": ["pexpireat", -3, ["write", "fast"], 1, 1, 1, ["@keyspace", "@fast", "@write"], [], [], []], "pexpiretime": ["pexpiretime", 2, ["readonly", "fast"], 1, 1, 1, ["@keyspace", "@fast", "@read"], [], [], []], "pfadd": ["pfadd", -2, ["write", "denyoom", "fast"], 1, 1, 1, ["@fast", "@hyperloglog", "@write"], [], [], []], "pfcount": ["pfcount", -2, ["readonly"], 1, 1, 1, ["@read", "@hyperloglog", "@slow"], [], [], []], "pfmerge": ["pfmerge", -2, ["write", "denyoom"], 1, 2, 1, ["@hyperloglog", "@slow", "@write"], [], [], []], "ping": ["ping", -1, ["fast"], 0, 0, 0, ["@fast", "@connection"], [], [], []], "psetex": ["psetex", 4, ["write", "denyoom"], 1, 1, 1, ["@string", "@slow", "@write"], [], [], []], "psubscribe": ["psubscribe", -2, ["pubsub", "noscript", "loading", "stale"], 0, 0, 0, ["@slow", "@pubsub"], [], [], []], "pttl": ["pttl", 2, ["readonly", "fast"], 1, 1, 1, ["@keyspace", "@fast", "@read"], [], [], []], "publish": ["publish", 3, ["pubsub", "loading", "stale", "fast"], 0, 0, 0, ["@fast", "@pubsub"], [], [], []], "pubsub": ["pubsub", -2, [], 0, 0, 0, ["@slow"], [], [], [["pubsub|channels", -2, ["pubsub", "loading", "stale"], 0, 0, 0, ["@slow", "@pubsub"], [], [], []], ["pubsub|help", 2, ["loading", "stale"], 0, 0, 0, ["@slow"], [], [], []], ["pubsub|numpat", 2, ["pubsub", "loading", "stale"], 0, 0, 0, ["@slow", "@pubsub"], [], [], []], ["pubsub|numsub", -2, ["pubsub", "loading", "stale"], 0, 0, 0, ["@slow", "@pubsub"], [], [], []], ["pubsub|shardchannels", -2, ["pubsub", "loading", "stale"], 0, 0, 0, ["@slow", "@pubsub"], [], [], []], ["pubsub|shardnumsub", -2, ["pubsub", "loading", "stale"], 0, 0, 0, ["@slow", "@pubsub"], [], [], []], ["pubsub|channels", -2, ["pubsub", "loading", "stale"], 0, 0, 0, ["@slow", "@pubsub"], [], [], []], ["pubsub|help", 2, ["loading", "stale"], 0, 0, 0, ["@slow"], [], [], []], ["pubsub|numpat", 2, ["pubsub", "loading", "stale"], 0, 0, 0, ["@slow", "@pubsub"], [], [], []], ["pubsub|numsub", -2, ["pubsub", "loading", "stale"], 0, 0, 0, ["@slow", "@pubsub"], [], [], []], ["pubsub|shardchannels", -2, ["pubsub", "loading", "stale"], 0, 0, 0, ["@slow", "@pubsub"], [], [], []], ["pubsub|shardnumsub", -2, ["pubsub", "loading", "stale"], 0, 0, 0, ["@slow", "@pubsub"], [], [], []]]], "pubsub channels": ["pubsub|channels", -2, ["pubsub", "loading", "stale"], 0, 0, 0, ["@slow", "@pubsub"], [], [], []], "pubsub help": ["pubsub|help", 2, ["loading", "stale"], 0, 0, 0, ["@slow"], [], [], []], "pubsub numpat": ["pubsub|numpat", 2, ["pubsub", "loading", "stale"], 0, 0, 0, ["@slow", "@pubsub"], [], [], []], "pubsub numsub": ["pubsub|numsub", -2, ["pubsub", "loading", "stale"], 0, 0, 0, ["@slow", "@pubsub"], [], [], []], "pubsub shardchannels": ["pubsub|shardchannels", -2, ["pubsub", "loading", "stale"], 0, 0, 0, ["@slow", "@pubsub"], [], [], []], "pubsub shardnumsub": ["pubsub|shardnumsub", -2, ["pubsub", "loading", "stale"], 0, 0, 0, ["@slow", "@pubsub"], [], [], []], "punsubscribe": ["punsubscribe", -1, ["pubsub", "noscript", "loading", "stale"], 0, 0, 0, ["@slow", "@pubsub"], [], [], []], "randomkey": ["randomkey", 1, ["readonly"], 0, 0, 0, ["@keyspace", "@read", "@slow"], [], [], []], "rename": ["rename", 3, ["write"], 1, 2, 1, ["@keyspace", "@slow", "@write"], [], [], [["renamenx", 3, ["write", "fast"], 1, 2, 1, ["@keyspace", "@fast", "@write"], [], [], []]]], "renamenx": ["renamenx", 3, ["write", "fast"], 1, 2, 1, ["@keyspace", "@fast", "@write"], [], [], []], "restore": ["restore", -4, ["write", "denyoom"], 1, 1, 1, ["@keyspace", "@dangerous", "@slow", "@write"], [], [], [["restore-asking", -4, ["write", "denyoom", "asking"], 1, 1, 1, ["@keyspace", "@dangerous", "@slow", "@write"], [], [], []]]], "rpop": ["rpop", -2, ["write", "fast"], 1, 1, 1, ["@list", "@fast", "@write"], [], [], [["rpoplpush", 3, ["write", "denyoom"], 1, 2, 1, ["@list", "@slow", "@write"], [], [], []]]], "rpoplpush": ["rpoplpush", 3, ["write", "denyoom"], 1, 2, 1, ["@list", "@slow", "@write"], [], [], []], "rpush": ["rpush", -3, ["write", "denyoom", "fast"], 1, 1, 1, ["@list", "@fast", "@write"], [], [], [["rpushx", -3, ["write", "denyoom", "fast"], 1, 1, 1, ["@list", "@fast", "@write"], [], [], []]]], "rpushx": ["rpushx", -3, ["write", "denyoom", "fast"], 1, 1, 1, ["@list", "@fast", "@write"], [], [], []], "sadd": ["sadd", -3, ["write", "denyoom", "fast"], 1, 1, 1, ["@fast", "@set", "@write"], [], [], []], "save": ["save", 1, ["admin", "noscript", "no_async_loading", "no_multi"], 0, 0, 0, ["@admin", "@slow", "@dangerous"], [], [], []], "scan": ["scan", -2, ["readonly"], 0, 0, 0, ["@keyspace", "@read", "@slow"], [], [], []], "scard": ["scard", 2, ["readonly", "fast"], 1, 1, 1, ["@fast", "@set", "@read"], [], [], []], "script": ["script", -2, [], 0, 0, 0, ["@slow"], [], [], [["script|debug", 3, ["noscript"], 0, 0, 0, ["@scripting", "@slow"], [], [], []], ["script|exists", -3, ["noscript"], 0, 0, 0, ["@scripting", "@slow"], [], [], []], ["script|flush", -2, ["noscript"], 0, 0, 0, ["@scripting", "@slow"], [], [], []], ["script|help", 2, ["loading", "stale"], 0, 0, 0, ["@scripting", "@slow"], [], [], []], ["script|kill", 2, ["noscript", "allow_busy"], 0, 0, 0, ["@scripting", "@slow"], [], [], []], ["script|load", 3, ["noscript", "stale"], 0, 0, 0, ["@scripting", "@slow"], [], [], []], ["script|exists", -3, ["noscript"], 0, 0, 0, ["@scripting", "@slow"], [], [], []], ["script|flush", -2, ["noscript"], 0, 0, 0, ["@scripting", "@slow"], [], [], []], ["script|help", 2, ["loading", "stale"], 0, 0, 0, ["@scripting", "@slow"], [], [], []], ["script|load", 3, ["noscript", "stale"], 0, 0, 0, ["@scripting", "@slow"], [], [], []]]], "script exists": ["script|exists", -3, ["noscript"], 0, 0, 0, ["@scripting", "@slow"], [], [], []], "script flush": ["script|flush", -2, ["noscript"], 0, 0, 0, ["@scripting", "@slow"], [], [], []], "script help": ["script|help", 2, ["loading", "stale"], 0, 0, 0, ["@scripting", "@slow"], [], [], []], "script load": ["script|load", 3, ["noscript", "stale"], 0, 0, 0, ["@scripting", "@slow"], [], [], []], "sdiff": ["sdiff", -2, ["readonly"], 1, 1, 1, ["@set", "@read", "@slow"], [], [], [["sdiffstore", -3, ["write", "denyoom"], 1, 2, 1, ["@set", "@slow", "@write"], [], [], []]]], "sdiffstore": ["sdiffstore", -3, ["write", "denyoom"], 1, 2, 1, ["@set", "@slow", "@write"], [], [], []], "select": ["select", 2, ["loading", "stale", "fast"], 0, 0, 0, ["@fast", "@connection"], [], [], []], "set": ["set", -3, ["write", "denyoom"], 1, 1, 1, ["@string", "@slow", "@write"], [], [], [["setbit", 4, ["write", "denyoom"], 1, 1, 1, ["@bitmap", "@slow", "@write"], [], [], []], ["setex", 4, ["write", "denyoom"], 1, 1, 1, ["@string", "@slow", "@write"], [], [], []], ["setnx", 3, ["write", "denyoom", "fast"], 1, 1, 1, ["@fast", "@string", "@write"], [], [], []], ["setrange", 4, ["write", "denyoom"], 1, 1, 1, ["@string", "@slow", "@write"], [], [], []]]], "setbit": ["setbit", 4, ["write", "denyoom"], 1, 1, 1, ["@bitmap", "@slow", "@write"], [], [], []], "setex": ["setex", 4, ["write", "denyoom"], 1, 1, 1, ["@string", "@slow", "@write"], [], [], []], "setnx": ["setnx", 3, ["write", "denyoom", "fast"], 1, 1, 1, ["@fast", "@string", "@write"], [], [], []], "setrange": ["setrange", 4, ["write", "denyoom"], 1, 1, 1, ["@string", "@slow", "@write"], [], [], []], "sinter": ["sinter", -2, ["readonly"], 1, 1, 1, ["@set", "@read", "@slow"], [], [], [["sintercard", -3, ["readonly", "movablekeys"], 1, 1, 1, ["@set", "@read", "@slow"], [], [], []], ["sinterstore", -3, ["write", "denyoom"], 1, 2, 1, ["@set", "@slow", "@write"], [], [], []]]], "sintercard": ["sintercard", -3, ["readonly", "movablekeys"], 1, 1, 1, ["@set", "@read", "@slow"], [], [], []], "sinterstore": ["sinterstore", -3, ["write", "denyoom"], 1, 2, 1, ["@set", "@slow", "@write"], [], [], []], "sismember": ["sismember", 3, ["readonly", "fast"], 1, 1, 1, ["@fast", "@set", "@read"], [], [], []], "smembers": ["smembers", 2, ["readonly"], 1, 1, 1, ["@set", "@read", "@slow"], [], [], []], "smismember": ["s<PERSON><PERSON><PERSON>", -3, ["readonly", "fast"], 1, 1, 1, ["@fast", "@set", "@read"], [], [], []], "smove": ["smove", 4, ["write", "fast"], 1, 2, 1, ["@fast", "@set", "@write"], [], [], []], "sort": ["sort", -2, ["write", "denyoom", "movablekeys"], 1, 0, 1, ["@list", "@dangerous", "@sortedset", "@write", "@set", "@slow"], [], [], [["sort_ro", -2, ["readonly", "movablekeys"], 1, 0, 1, ["@list", "@dangerous", "@sortedset", "@read", "@set", "@slow"], [], [], []]]], "sort_ro": ["sort_ro", -2, ["readonly", "movablekeys"], 1, 0, 1, ["@list", "@dangerous", "@sortedset", "@read", "@set", "@slow"], [], [], []], "spop": ["spop", -2, ["write", "fast"], 1, 1, 1, ["@fast", "@set", "@write"], [], [], []], "spublish": ["spublish", 3, ["pubsub", "loading", "stale", "fast"], 1, 1, 1, ["@fast", "@pubsub"], [], [], []], "srandmember": ["srandmember", -2, ["readonly"], 1, 1, 1, ["@set", "@read", "@slow"], [], [], []], "srem": ["srem", -3, ["write", "fast"], 1, 1, 1, ["@fast", "@set", "@write"], [], [], []], "sscan": ["sscan", -3, ["readonly"], 1, 1, 1, ["@set", "@read", "@slow"], [], [], []], "ssubscribe": ["ssubscribe", -2, ["pubsub", "noscript", "loading", "stale"], 1, 1, 1, ["@slow", "@pubsub"], [], [], []], "strlen": ["strlen", 2, ["readonly", "fast"], 1, 1, 1, ["@fast", "@read", "@string"], [], [], []], "subscribe": ["subscribe", -2, ["pubsub", "noscript", "loading", "stale"], 0, 0, 0, ["@slow", "@pubsub"], [], [], []], "substr": ["substr", 4, ["readonly"], 1, 1, 1, ["@read", "@string", "@slow"], [], [], []], "sunion": ["sunion", -2, ["readonly"], 1, 1, 1, ["@set", "@read", "@slow"], [], [], [["sunionstore", -3, ["write", "denyoom"], 1, 2, 1, ["@set", "@slow", "@write"], [], [], []]]], "sunionstore": ["sunionstore", -3, ["write", "denyoom"], 1, 2, 1, ["@set", "@slow", "@write"], [], [], []], "sunsubscribe": ["sunsubscribe", -1, ["pubsub", "noscript", "loading", "stale"], 1, 1, 1, ["@slow", "@pubsub"], [], [], []], "swapdb": ["swapdb", 3, ["write", "fast"], 0, 0, 0, ["@keyspace", "@dangerous", "@fast", "@write"], [], [], []], "time": ["time", 1, ["loading", "stale", "fast"], 0, 0, 0, ["@fast"], [], [], []], "ttl": ["ttl", 2, ["readonly", "fast"], 1, 1, 1, ["@keyspace", "@fast", "@read"], [], [], []], "type": ["type", 2, ["readonly", "fast"], 1, 1, 1, ["@keyspace", "@fast", "@read"], [], [], []], "unlink": ["unlink", -2, ["write", "fast"], 1, 1, 1, ["@keyspace", "@fast", "@write"], [], [], []], "unsubscribe": ["unsubscribe", -1, ["pubsub", "noscript", "loading", "stale"], 0, 0, 0, ["@slow", "@pubsub"], [], [], []], "unwatch": ["unwatch", 1, ["noscript", "loading", "stale", "fast", "allow_busy"], 0, 0, 0, ["@transaction", "@fast"], [], [], []], "watch": ["watch", -2, ["noscript", "loading", "stale", "fast", "allow_busy"], 1, 1, 1, ["@transaction", "@fast"], [], [], []], "xack": ["xack", -4, ["write", "fast"], 1, 1, 1, ["@stream", "@fast", "@write"], [], [], []], "xadd": ["xadd", -5, ["write", "denyoom", "fast"], 1, 1, 1, ["@stream", "@fast", "@write"], [], [], []], "xautoclaim": ["xaut<PERSON><PERSON>m", -6, ["write", "fast"], 1, 1, 1, ["@stream", "@fast", "@write"], [], [], []], "xclaim": ["xclaim", -6, ["write", "fast"], 1, 1, 1, ["@stream", "@fast", "@write"], [], [], []], "xdel": ["xdel", -3, ["write", "fast"], 1, 1, 1, ["@stream", "@fast", "@write"], [], [], []], "xgroup create": ["xgroup|create", -5, ["write", "denyoom"], 2, 2, 1, ["@stream", "@slow", "@write"], [], [], [["xgroup|createconsumer", 5, ["write", "denyoom"], 2, 2, 1, ["@stream", "@slow", "@write"], [], [], []]]], "xgroup": ["xgroup", -1, [], 0, 0, 0, [], [], [], [["xgroup|create", -5, ["write", "denyoom"], 2, 2, 1, ["@stream", "@slow", "@write"], [], [], [["xgroup|createconsumer", 5, ["write", "denyoom"], 2, 2, 1, ["@stream", "@slow", "@write"], [], [], []]]], ["xgroup|createconsumer", 5, ["write", "denyoom"], 2, 2, 1, ["@stream", "@slow", "@write"], [], [], []], ["xgroup|delconsumer", 5, ["write"], 2, 2, 1, ["@stream", "@slow", "@write"], [], [], []], ["xgroup|destroy", 4, ["write"], 2, 2, 1, ["@stream", "@slow", "@write"], [], [], []], ["xgroup|setid", -5, ["write"], 2, 2, 1, ["@stream", "@slow", "@write"], [], [], []]]], "xgroup createconsumer": ["xgroup|createconsumer", 5, ["write", "denyoom"], 2, 2, 1, ["@stream", "@slow", "@write"], [], [], []], "xgroup delconsumer": ["xgroup|delconsumer", 5, ["write"], 2, 2, 1, ["@stream", "@slow", "@write"], [], [], []], "xgroup destroy": ["xgroup|destroy", 4, ["write"], 2, 2, 1, ["@stream", "@slow", "@write"], [], [], []], "xgroup setid": ["xgroup|setid", -5, ["write"], 2, 2, 1, ["@stream", "@slow", "@write"], [], [], []], "xinfo consumers": ["xinfo|consumers", 4, ["readonly"], 2, 2, 1, ["@stream", "@read", "@slow"], [], [], []], "xinfo": ["xinfo", -1, [], 0, 0, 0, [], [], [], [["xinfo|consumers", 4, ["readonly"], 2, 2, 1, ["@stream", "@read", "@slow"], [], [], []], ["xinfo|groups", 3, ["readonly"], 2, 2, 1, ["@stream", "@read", "@slow"], [], [], []], ["xinfo|stream", -3, ["readonly"], 2, 2, 1, ["@stream", "@read", "@slow"], [], [], []]]], "xinfo groups": ["xinfo|groups", 3, ["readonly"], 2, 2, 1, ["@stream", "@read", "@slow"], [], [], []], "xinfo stream": ["xinfo|stream", -3, ["readonly"], 2, 2, 1, ["@stream", "@read", "@slow"], [], [], []], "xlen": ["xlen", 2, ["readonly", "fast"], 1, 1, 1, ["@stream", "@fast", "@read"], [], [], []], "xpending": ["xpending", -3, ["readonly"], 1, 1, 1, ["@stream", "@read", "@slow"], [], [], []], "xrange": ["xrange", -4, ["readonly"], 1, 1, 1, ["@stream", "@read", "@slow"], [], [], []], "xread": ["xread", -4, ["readonly", "blocking", "movablekeys"], 0, 0, 1, ["@stream", "@read", "@blocking", "@slow"], [], [], [["xreadgroup", -7, ["write", "blocking", "movablekeys"], 0, 0, 1, ["@stream", "@blocking", "@slow", "@write"], [], [], []]]], "xreadgroup": ["xreadgroup", -7, ["write", "blocking", "movablekeys"], 0, 0, 1, ["@stream", "@blocking", "@slow", "@write"], [], [], []], "xrevrange": ["xrevrange", -4, ["readonly"], 1, 1, 1, ["@stream", "@read", "@slow"], [], [], []], "xtrim": ["xtrim", -4, ["write"], 1, 1, 1, ["@stream", "@slow", "@write"], [], [], []], "zadd": ["zadd", -4, ["write", "denyoom", "fast"], 1, 1, 1, ["@fast", "@sortedset", "@write"], [], [], []], "zcard": ["zcard", 2, ["readonly", "fast"], 1, 1, 1, ["@fast", "@read", "@sortedset"], [], [], []], "zcount": ["zcount", 4, ["readonly", "fast"], 1, 1, 1, ["@fast", "@read", "@sortedset"], [], [], []], "zdiff": ["zdiff", -3, ["readonly", "movablekeys"], 1, 1, 1, ["@read", "@sortedset", "@slow"], [], [], [["zdiffstore", -4, ["write", "denyoom", "movablekeys"], 1, 2, 1, ["@sortedset", "@slow", "@write"], [], [], []]]], "zdiffstore": ["zdiffstore", -4, ["write", "denyoom", "movablekeys"], 1, 2, 1, ["@sortedset", "@slow", "@write"], [], [], []], "zincrby": ["zincrby", 4, ["write", "denyoom", "fast"], 1, 1, 1, ["@fast", "@sortedset", "@write"], [], [], []], "zinter": ["zinter", -3, ["readonly", "movablekeys"], 1, 1, 1, ["@read", "@sortedset", "@slow"], [], [], [["zintercard", -3, ["readonly", "movablekeys"], 1, 1, 1, ["@read", "@sortedset", "@slow"], [], [], []], ["zinterstore", -4, ["write", "denyoom", "movablekeys"], 1, 2, 1, ["@sortedset", "@slow", "@write"], [], [], []]]], "zintercard": ["zintercard", -3, ["readonly", "movablekeys"], 1, 1, 1, ["@read", "@sortedset", "@slow"], [], [], []], "zinterstore": ["zinterstore", -4, ["write", "denyoom", "movablekeys"], 1, 2, 1, ["@sortedset", "@slow", "@write"], [], [], []], "zlexcount": ["zlexcount", 4, ["readonly", "fast"], 1, 1, 1, ["@fast", "@read", "@sortedset"], [], [], []], "zmpop": ["zmpop", -4, ["write", "movablekeys"], 1, 1, 1, ["@sortedset", "@slow", "@write"], [], [], []], "zmscore": ["zmscore", -3, ["readonly", "fast"], 1, 1, 1, ["@fast", "@read", "@sortedset"], [], [], []], "zpopmax": ["zpopmax", -2, ["write", "fast"], 1, 1, 1, ["@fast", "@sortedset", "@write"], [], [], []], "zpopmin": ["zpopmin", -2, ["write", "fast"], 1, 1, 1, ["@fast", "@sortedset", "@write"], [], [], []], "zrandmember": ["zrandmember", -2, ["readonly"], 1, 1, 1, ["@read", "@sortedset", "@slow"], [], [], []], "zrange": ["zrange", -4, ["readonly"], 1, 1, 1, ["@read", "@sortedset", "@slow"], [], [], [["zrangebylex", -4, ["readonly"], 1, 1, 1, ["@read", "@sortedset", "@slow"], [], [], []], ["zrangebyscore", -4, ["readonly"], 1, 1, 1, ["@read", "@sortedset", "@slow"], [], [], []], ["zrangestore", -5, ["write", "denyoom"], 1, 2, 1, ["@sortedset", "@slow", "@write"], [], [], []]]], "zrangebylex": ["zrangebylex", -4, ["readonly"], 1, 1, 1, ["@read", "@sortedset", "@slow"], [], [], []], "zrangebyscore": ["zrangebyscore", -4, ["readonly"], 1, 1, 1, ["@read", "@sortedset", "@slow"], [], [], []], "zrangestore": ["zrangestore", -5, ["write", "denyoom"], 1, 2, 1, ["@sortedset", "@slow", "@write"], [], [], []], "zrank": ["zrank", -3, ["readonly", "fast"], 1, 1, 1, ["@fast", "@read", "@sortedset"], [], [], []], "zrem": ["zrem", -3, ["write", "fast"], 1, 1, 1, ["@fast", "@sortedset", "@write"], [], [], [["zremrangebylex", 4, ["write"], 1, 1, 1, ["@sortedset", "@slow", "@write"], [], [], []], ["zremrangebyrank", 4, ["write"], 1, 1, 1, ["@sortedset", "@slow", "@write"], [], [], []], ["zremrangebyscore", 4, ["write"], 1, 1, 1, ["@sortedset", "@slow", "@write"], [], [], []]]], "zremrangebylex": ["zremrangebylex", 4, ["write"], 1, 1, 1, ["@sortedset", "@slow", "@write"], [], [], []], "zremrangebyrank": ["zremrangebyrank", 4, ["write"], 1, 1, 1, ["@sortedset", "@slow", "@write"], [], [], []], "zremrangebyscore": ["zremrangebyscore", 4, ["write"], 1, 1, 1, ["@sortedset", "@slow", "@write"], [], [], []], "zrevrange": ["zrevrange", -4, ["readonly"], 1, 1, 1, ["@read", "@sortedset", "@slow"], [], [], [["zrevrangebylex", -4, ["readonly"], 1, 1, 1, ["@read", "@sortedset", "@slow"], [], [], []], ["zrevrangebyscore", -4, ["readonly"], 1, 1, 1, ["@read", "@sortedset", "@slow"], [], [], []]]], "zrevrangebylex": ["zrevrangebylex", -4, ["readonly"], 1, 1, 1, ["@read", "@sortedset", "@slow"], [], [], []], "zrevrangebyscore": ["zrevrangebyscore", -4, ["readonly"], 1, 1, 1, ["@read", "@sortedset", "@slow"], [], [], []], "zrevrank": ["zrevrank", -3, ["readonly", "fast"], 1, 1, 1, ["@fast", "@read", "@sortedset"], [], [], []], "zscan": ["zscan", -3, ["readonly"], 1, 1, 1, ["@read", "@sortedset", "@slow"], [], [], []], "zscore": ["zscore", 3, ["readonly", "fast"], 1, 1, 1, ["@fast", "@read", "@sortedset"], [], [], []], "zunion": ["zunion", -3, ["readonly", "movablekeys"], 1, 1, 1, ["@read", "@sortedset", "@slow"], [], [], [["zunionst<PERSON>", -4, ["write", "denyoom", "movablekeys"], 1, 2, 1, ["@sortedset", "@slow", "@write"], [], [], []]]], "zunionstore": ["zunionst<PERSON>", -4, ["write", "denyoom", "movablekeys"], 1, 2, 1, ["@sortedset", "@slow", "@write"], [], [], []], "json.del": ["json.del", -1, [], 0, 0, 0, ["@json"], [], [], []], "json.forget": ["json.forget", -1, [], 0, 0, 0, ["@json"], [], [], []], "json.get": ["json.get", -1, [], 0, 0, 0, ["@json"], [], [], []], "json.toggle": ["json.toggle", -1, [], 0, 0, 0, ["@json"], [], [], []], "json.clear": ["json.clear", -1, [], 0, 0, 0, ["@json"], [], [], []], "json.set": ["json.set", -1, [], 0, 0, 0, ["@json"], [], [], []], "json.mset": ["json.mset", -1, [], 0, 0, 0, ["@json"], [], [], []], "json.merge": ["json.merge", -1, [], 0, 0, 0, ["@json"], [], [], []], "json.mget": ["json.mget", -1, [], 0, 0, 0, ["@json"], [], [], []], "json.numincrby": ["json.numincrby", -1, [], 0, 0, 0, ["@json"], [], [], []], "json.nummultby": ["json.nummultby", -1, [], 0, 0, 0, ["@json"], [], [], []], "json.strappend": ["json.strappend", -1, [], 0, 0, 0, ["@json"], [], [], []], "json.strlen": ["json.strlen", -1, [], 0, 0, 0, ["@json"], [], [], []], "json.arrappend": ["json.arrappend", -1, [], 0, 0, 0, ["@json"], [], [], []], "json.arrindex": ["json.arrindex", -1, [], 0, 0, 0, ["@json"], [], [], []], "json.arrinsert": ["json.a<PERSON>", -1, [], 0, 0, 0, ["@json"], [], [], []], "json.arrlen": ["json.arrlen", -1, [], 0, 0, 0, ["@json"], [], [], []], "json.arrpop": ["json.arrpop", -1, [], 0, 0, 0, ["@json"], [], [], []], "json.arrtrim": ["json.arrtrim", -1, [], 0, 0, 0, ["@json"], [], [], []], "json.objkeys": ["j<PERSON>.ob<PERSON><PERSON><PERSON>", -1, [], 0, 0, 0, ["@json"], [], [], []], "json.objlen": ["json.objlen", -1, [], 0, 0, 0, ["@json"], [], [], []], "json.type": ["json.type", -1, [], 0, 0, 0, ["@json"], [], [], []], "ts.create": ["ts.create", -1, [], 0, 0, 0, ["@timeseries"], [], [], [["ts.<PERSON><PERSON>le", -1, [], 0, 0, 0, ["@timeseries"], [], [], []]]], "ts.del": ["ts.del", -1, [], 0, 0, 0, ["@timeseries"], [], [], [["ts.deleterule", -1, [], 0, 0, 0, ["@timeseries"], [], [], []]]], "ts.alter": ["ts.alter", -1, [], 0, 0, 0, ["@timeseries"], [], [], []], "ts.add": ["ts.add", -1, [], 0, 0, 0, ["@timeseries"], [], [], []], "ts.madd": ["ts.madd", -1, [], 0, 0, 0, ["@timeseries"], [], [], []], "ts.incrby": ["ts.incrby", -1, [], 0, 0, 0, ["@timeseries"], [], [], []], "ts.decrby": ["ts.dec<PERSON>", -1, [], 0, 0, 0, ["@timeseries"], [], [], []], "ts.createrule": ["ts.<PERSON><PERSON>le", -1, [], 0, 0, 0, ["@timeseries"], [], [], []], "ts.deleterule": ["ts.deleterule", -1, [], 0, 0, 0, ["@timeseries"], [], [], []], "ts.range": ["ts.range", -1, [], 0, 0, 0, ["@timeseries"], [], [], []], "ts.revrange": ["ts.revrange", -1, [], 0, 0, 0, ["@timeseries"], [], [], []], "ts.mrange": ["ts.m<PERSON>e", -1, [], 0, 0, 0, ["@timeseries"], [], [], []], "ts.mrevrange": ["ts.mre<PERSON><PERSON>e", -1, [], 0, 0, 0, ["@timeseries"], [], [], []], "ts.get": ["ts.get", -1, [], 0, 0, 0, ["@timeseries"], [], [], []], "ts.mget": ["ts.mget", -1, [], 0, 0, 0, ["@timeseries"], [], [], []], "ts.info": ["ts.info", -1, [], 0, 0, 0, ["@timeseries"], [], [], []], "ts.queryindex": ["ts.queryindex", -1, [], 0, 0, 0, ["@timeseries"], [], [], []], "bf.reserve": ["bf.reserve", -1, [], 0, 0, 0, ["@bloom"], [], [], []], "bf.add": ["bf.add", -1, [], 0, 0, 0, ["@bloom"], [], [], []], "bf.madd": ["bf.madd", -1, [], 0, 0, 0, ["@bloom"], [], [], []], "bf.insert": ["bf.insert", -1, [], 0, 0, 0, ["@bloom"], [], [], []], "bf.exists": ["bf.exists", -1, [], 0, 0, 0, ["@bloom"], [], [], []], "bf.mexists": ["bf.mexists", -1, [], 0, 0, 0, ["@bloom"], [], [], []], "bf.scandump": ["bf.scandump", -1, [], 0, 0, 0, ["@bloom"], [], [], []], "bf.loadchunk": ["bf.loadchunk", -1, [], 0, 0, 0, ["@bloom"], [], [], []], "bf.info": ["bf.info", -1, [], 0, 0, 0, ["@bloom"], [], [], []], "bf.card": ["bf.card", -1, [], 0, 0, 0, ["@bloom"], [], [], []], "cf.reserve": ["cf.reserve", -1, [], 0, 0, 0, ["@cuckoo"], [], [], []], "cf.add": ["cf.add", -1, [], 0, 0, 0, ["@cuckoo"], [], [], [["cf.addnx", -1, [], 0, 0, 0, ["@cuckoo"], [], [], []]]], "cf.addnx": ["cf.addnx", -1, [], 0, 0, 0, ["@cuckoo"], [], [], []], "cf.insert": ["cf.insert", -1, [], 0, 0, 0, ["@cuckoo"], [], [], [["cf.insertnx", -1, [], 0, 0, 0, ["@cuckoo"], [], [], []]]], "cf.insertnx": ["cf.insertnx", -1, [], 0, 0, 0, ["@cuckoo"], [], [], []], "cf.exists": ["cf.exists", -1, [], 0, 0, 0, ["@cuckoo"], [], [], []], "cf.mexists": ["cf.mexists", -1, [], 0, 0, 0, ["@cuckoo"], [], [], []], "cf.del": ["cf.del", -1, [], 0, 0, 0, ["@cuckoo"], [], [], []], "cf.count": ["cf.count", -1, [], 0, 0, 0, ["@cuckoo"], [], [], []], "cf.scandump": ["cf.scandump", -1, [], 0, 0, 0, ["@cuckoo"], [], [], []], "cf.loadchunk": ["cf.loadchunk", -1, [], 0, 0, 0, ["@cuckoo"], [], [], []], "cf.info": ["cf.info", -1, [], 0, 0, 0, ["@cuckoo"], [], [], []], "cms.initbydim": ["cms.initbydim", -1, [], 0, 0, 0, ["@cms"], [], [], []], "cms.initbyprob": ["cms.initbyprob", -1, [], 0, 0, 0, ["@cms"], [], [], []], "cms.incrby": ["cms.incrby", -1, [], 0, 0, 0, ["@cms"], [], [], []], "cms.query": ["cms.query", -1, [], 0, 0, 0, ["@cms"], [], [], []], "cms.merge": ["cms.merge", -1, [], 0, 0, 0, ["@cms"], [], [], []], "cms.info": ["cms.info", -1, [], 0, 0, 0, ["@cms"], [], [], []], "topk.reserve": ["topk.reserve", -1, [], 0, 0, 0, ["@topk"], [], [], []], "topk.add": ["topk.add", -1, [], 0, 0, 0, ["@topk"], [], [], []], "topk.incrby": ["topk.incrby", -1, [], 0, 0, 0, ["@topk"], [], [], []], "topk.query": ["topk.query", -1, [], 0, 0, 0, ["@topk"], [], [], []], "topk.count": ["topk.count", -1, [], 0, 0, 0, ["@topk"], [], [], []], "topk.list": ["topk.list", -1, [], 0, 0, 0, ["@topk"], [], [], []], "topk.info": ["topk.info", -1, [], 0, 0, 0, ["@topk"], [], [], []], "tdigest.create": ["tdigest.create", -1, [], 0, 0, 0, ["@tdigest"], [], [], []], "tdigest.reset": ["tdigest.reset", -1, [], 0, 0, 0, ["@tdigest"], [], [], []], "tdigest.add": ["tdigest.add", -1, [], 0, 0, 0, ["@tdigest"], [], [], []], "tdigest.merge": ["tdigest.merge", -1, [], 0, 0, 0, ["@tdigest"], [], [], []], "tdigest.min": ["tdigest.min", -1, [], 0, 0, 0, ["@tdigest"], [], [], []], "tdigest.max": ["tdigest.max", -1, [], 0, 0, 0, ["@tdigest"], [], [], []], "tdigest.quantile": ["tdigest.quantile", -1, [], 0, 0, 0, ["@tdigest"], [], [], []], "tdigest.cdf": ["tdigest.cdf", -1, [], 0, 0, 0, ["@tdigest"], [], [], []], "tdigest.trimmed_mean": ["tdigest.trimmed_mean", -1, [], 0, 0, 0, ["@tdigest"], [], [], []], "tdigest.rank": ["tdigest.rank", -1, [], 0, 0, 0, ["@tdigest"], [], [], []], "tdigest.revrank": ["tdigest.revrank", -1, [], 0, 0, 0, ["@tdigest"], [], [], []], "tdigest.byrank": ["tdigest.byrank", -1, [], 0, 0, 0, ["@tdigest"], [], [], []], "tdigest.byrevrank": ["tdigest.byrevrank", -1, [], 0, 0, 0, ["@tdigest"], [], [], []], "tdigest.info": ["tdigest.info", -1, [], 0, 0, 0, ["@tdigest"], [], [], []]}