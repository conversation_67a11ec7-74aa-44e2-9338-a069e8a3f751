Metadata-Version: 2.1
Name: mysql-connector-python
Version: 9.3.0
Summary: A self-contained Python driver for communicating with MySQL servers, using an API that is compliant with the Python Database API Specification v2.0 (PEP 249).
Home-page: UNKNOWN
Author: Oracle and/or its affiliates
Author-email: 
License: GNU GPLv2 (with FOSS License Exception)
Project-URL: Homepage, https://dev.mysql.com/doc/connector-python/en/
Project-URL: Documentation, https://dev.mysql.com/doc/connector-python/en/
Project-URL: Downloads, https://dev.mysql.com/downloads/connector/python/
Project-URL: Release Notes, https://dev.mysql.com/doc/relnotes/connector-python/en/
Project-URL: Source Code, https://github.com/mysql/mysql-connector-python
Project-URL: Bug System, https://bugs.mysql.com/
Project-URL: Slack, https://mysqlcommunity.slack.com/messages/connectors
Project-URL: Forums, https://forums.mysql.com/list.php?50
Project-URL: Blog, https://blogs.oracle.com/mysql/
Keywords: mysql,database,db,connector,driver
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Education
Classifier: License :: OSI Approved :: GNU General Public License (GPL)
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: POSIX :: Linux
Classifier: Operating System :: Unix
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: Database
Classifier: Topic :: Software Development
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Typing :: Typed
Requires-Python: >=3.9
Description-Content-Type: text/x-rst
Provides-Extra: dns-srv
Requires-Dist: dnspython==2.6.1; extra == "dns-srv"
Provides-Extra: fido2
Requires-Dist: fido2==1.1.2; extra == "fido2"
Provides-Extra: gssapi
Requires-Dist: gssapi==1.8.3; extra == "gssapi"
Provides-Extra: telemetry
Requires-Dist: opentelemetry-api==1.18.0; extra == "telemetry"
Requires-Dist: opentelemetry-sdk==1.18.0; extra == "telemetry"
Requires-Dist: opentelemetry-exporter-otlp-proto-http==1.18.0; extra == "telemetry"

MySQL Connector/Python
======================

.. image:: https://img.shields.io/pypi/v/mysql-connector-python.svg
   :target: https://pypi.org/project/mysql-connector-python/
.. image:: https://img.shields.io/pypi/pyversions/mysql-connector-python.svg
   :target: https://pypi.org/project/mysql-connector-python/
.. image:: https://img.shields.io/pypi/l/mysql-connector-python.svg
   :target: https://pypi.org/project/mysql-connector-python/


MySQL Connector/Python enables Python programs to access MySQL databases, using
an API that is compliant with the `Python Database API Specification v2.0
(PEP 249) <https://www.python.org/dev/peps/pep-0249/>`_ - We refer to it as the
`Classic API <https://dev.mysql.com/doc/connector-python/en/connector-python-reference.html>`_.






Features
--------

* `Asynchronous Connectivity <https://dev.mysql.com/doc/connector-python/en/connector-python-asyncio.html>`_
* `C-extension <https://dev.mysql.com/doc/connector-python/en/connector-python-cext.html>`_
* `Telemetry <https://dev.mysql.com/doc/connector-python/en/connector-python-opentelemetry.html>`_





Installation
------------

Connector/Python contains the classic and XDevAPI connector APIs, which are
installed separately. Any of these can be installed from a binary
or source distribution.

Binaries are distributed in the following package formats:

* `RPM <https://docs.redhat.com/en/documentation/red_hat_enterprise_linux/8/html/packaging_and_distributing_software/introduction-to-rpm_packaging-and-distributing-software>`_
* `WHEEL <https://packaging.python.org/en/latest/discussions/package-formats/#what-is-a-wheel>`_

On the other hand, the source code is distributed as a compressed file
from which a wheel package can be built.

The recommended way to install Connector/Python is via `pip <https://pip.pypa.io/>`_,
which relies on WHEEL packages. For such a reason, it is the installation procedure
that is going to be described moving forward.

Please, refer to the official MySQL documentation `Connector/Python Installation
<https://dev.mysql.com/doc/connector-python/en/connector-python-installation.html>`_ to
know more about installing from an RPM, or building and installing a WHEEL package from
a source distribution.

Before installing a package with `pip <https://pip.pypa.io/>`_, it is strongly suggested
to have the most recent ``pip`` version installed on your system.
If your system already has ``pip`` installed, you might need to update it. Or you can use
the `standalone pip installer <https://pip.pypa.io/en/latest/installation/>`_.




.. code-block:: bash

    $ pip install mysql-connector-python






++++++++++++++++++++
Installation Options
++++++++++++++++++++

Connector packages included in MySQL Connector/Python allow you to install
optional dependencies to unleash certain functionalities.


.. code-block:: bash

    # 3rd party packages to unleash the telemetry functionality are installed
    $ pip install mysql-connector-python[telemetry]





This installation option can be seen as a shortcut to install all the
dependencies needed by a particular feature. Mind that this is optional
and you are free to install the required dependencies by yourself.


Available options:

* dns-srv
* gssapi
* fido2
* telemetry






Sample Code
-----------

.. code:: python

    import mysql.connector

    # Connect to server
    cnx = mysql.connector.connect(
        host="127.0.0.1",
        port=3306,
        user="mike",
        password="s3cre3t!")

    # Get a cursor
    cur = cnx.cursor()

    # Execute a query
    cur.execute("SELECT CURDATE()")

    # Fetch one result
    row = cur.fetchone()
    print("Current date is: {0}".format(row[0]))

    # Close connection
    cnx.close()






Additional Resources
--------------------

- `MySQL Connector/Python Developer Guide <https://dev.mysql.com/doc/connector-python/en/>`_

- `MySQL Connector/Python Forum <http://forums.mysql.com/list.php?50>`_
- `MySQL Public Bug Tracker <https://bugs.mysql.com>`_
- `Slack <https://mysqlcommunity.slack.com>`_ (`Sign-up <https://lefred.be/mysql-community-on-slack/>`_ required if you do not have an Oracle account)
- `Stack Overflow <https://stackoverflow.com/questions/tagged/mysql-connector-python>`_
- `Oracle Blogs <https://blogs.oracle.com/search.html?q=connector-python>`_




Contributing
------------

There are a few ways to contribute to the Connector/Python code. Please refer
to the `contributing guidelines <https://github.com/mysql/mysql-connector-python/blob/trunk/CONTRIBUTING.md>`_ for additional information.


License
-------

Please refer to the `README.txt <https://github.com/mysql/mysql-connector-python/blob/trunk/README.txt>`_ and `LICENSE.txt <https://github.com/mysql/mysql-connector-python/blob/trunk/LICENSE.txt>`_
files, available in this repository, for further details.


