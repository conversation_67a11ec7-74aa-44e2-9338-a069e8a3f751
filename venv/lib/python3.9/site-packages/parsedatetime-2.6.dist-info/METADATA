Metadata-Version: 2.1
Name: parsedatetime
Version: 2.6
Summary: Parse human-readable date/time text.
Home-page: https://github.com/bear/parsedatetime
Author: <PERSON>
Author-email: <EMAIL>
License: Apache License 2.0
Download-URL: https://pypi.python.org/pypi/parsedatetime
Platform: Any
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Operating System :: OS Independent
Classifier: Topic :: Text Processing
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7

parsedatetime
=============

Parse human-readable date/time strings.

Python 2.6 or greater is required for parsedatetime version 1.0 or greater.

While we still test with Python 2.6 we cannot guarantee that future changes will not break under 2.6

.. image:: https://img.shields.io/pypi/v/parsedatetime.svg
    :target: https://pypi.python.org/pypi/parsedatetime/
    :alt: Downloads

.. image:: https://travis-ci.org/bear/parsedatetime.svg?branch=master
    :target: https://travis-ci.org/bear/parsedatetime
    :alt: Travis CI

.. image:: http://codecov.io/github/bear/parsedatetime/coverage.svg?branch=master
    :target: http://codecov.io/github/bear/parsedatetime
    :alt: Codecov

.. image:: https://requires.io/github/bear/parsedatetime/requirements.svg?branch=master
     :target: https://requires.io/github/bear/parsedatetime/requirements/?branch=master
     :alt: Requirements Status

.. image:: https://dependencyci.com/github/bear/parsedatetime/badge
     :target: https://dependencyci.com/github/bear/parsedatetime
     :alt: Dependency Status

==========
Installing
==========

You can install parsedatetime using::

    pip install parsedatetime

=============
Running Tests
=============

From the source directory::

    make test

To run tests on several python versions, type ``make tox``::

  $ make tox
  [... tox creates a virtualenv for every python version and runs tests inside of each]
  py27: commands succeeded
  py35: commands succeeded

This assumes that you have the versions you want to test under installed as part of your
PyEnv environment::

    pyenv install -s 2.6.9
    pyenv install -s 2.7.11
    pyenv install -s 3.5.2
    pyenv install -s pypy-5.3
    pyenv global 2.7.11 3.5.2 2.6.9 pypy-5.3

The tests depend on PyICU being installed using the `pyicu-binary` package which removes the source build step. PyICU depends on icu4c which on macOS requires homebrew::

    brew install icu4c

The Makefile contains the macOS default values for them so you may need to tweak them.

===================
Using parsedatetime
===================

An example of how to use parsedatetime:


.. code:: python

    import parsedatetime

    cal = parsedatetime.Calendar()

    cal.parse("tomorrow")

To get it to a Python ``datetime`` object:


.. code:: python

    from datetime import datetime

    time_struct, parse_status = cal.parse("tomorrow")

    datetime(*time_struct[:6])

Parse datetime with timezone support (using pytz package):

.. code:: python

    import parsedatetime
    import pytz
    from pytz import timezone

    cal = parsedatetime.Calendar()

    datetime_obj, _ = cal.parseDT(datetimeString="tomorrow", tzinfo=timezone("US/Pacific"))

More detailed examples can be found in the examples directory.

=============
Documentation
=============

The generated documentation is included by default in the docs directory and can also be viewed online at https://bear.im/code/parsedatetime/docs/index.html

The docs can be generated by running::

    make docs

=====
Notes
=====

The ``Calendar`` class has a member property named ``ptc`` which is created during the class init method to be an instance
of ``parsedatetime_consts.CalendarConstants()``.

=======
History
=======

The code in parsedatetime has been implemented over the years in many different languages (C, Clipper, Delphi) as part of different custom/proprietary systems I've worked on.  Sadly the previous code is not "open" in any sense of that word.

When I went to work for Open Source Applications Foundation and realized that the Chandler project could benefit from my experience with parsing of date/time text I decided to start from scratch and implement the code using Python and make it truly open.

After working on the initial concept and creating something that could be shown to the Chandler folks, the code has now evolved to its current state with the help of the Chandler folks, most especially Darshana.


