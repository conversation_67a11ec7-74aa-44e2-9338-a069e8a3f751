import discord
import re
import json
from typing import Optional, Dict, List, Any
from datetime import datetime
import webcolors

# Constants
MAX_EMBED_TITLE_LENGTH = 256
MAX_EMBED_DESCRIPTION_LENGTH = 4096
MAX_EMBED_FIELD_NAME_LENGTH = 256
MAX_EMBED_FIELD_VALUE_LENGTH = 1024
MAX_EMBED_FOOTER_LENGTH = 2048
MAX_EMBED_AUTHOR_NAME_LENGTH = 256

class EmbedBuilder:
    def __init__(self, ctx=None):
        self.embed = discord.Embed()
        self.content = ""
        self.view = None
        self.ctx = ctx
    
    def parse_embed_string(self, embed_string: str) -> Dict[str, Any]:
        """Parse embed string format like JavaScript version"""
        try:
            # Apply placeholders to the entire string first
            embed_string = self._apply_placeholders(embed_string)

            # Split by { but keep track of proper nesting
            parts = self._split_embed_parts(embed_string)
            buttons = []

            for part in parts:
                if not part.strip():
                    continue

                try:
                    self._parse_embed_part(part, buttons)
                except Exception as e:
                    # Skip malformed parts but continue parsing
                    print(f"Warning: Skipped malformed embed part: {part[:50]}... Error: {e}")
                    continue

            if buttons:
                self.view = discord.ui.View()
                for button in buttons:
                    self.view.add_item(button)

            # Check if embed has any content
            has_embed_content = any([
                self.embed.title,
                self.embed.description,
                self.embed.fields,
                self.embed.author.name if self.embed.author else None,
                self.embed.footer.text if self.embed.footer else None,
                self.embed.image.url if self.embed.image else None,
                self.embed.thumbnail.url if self.embed.thumbnail else None,
                self.embed.color
            ])

            # If no embed content but we have message content, send only content
            if not has_embed_content and self.content:
                return {
                    'embed': None,
                    'content': self.content,
                    'view': self.view
                }

            # If we have embed content but no description, add a minimal one
            if has_embed_content and not self.embed.description and not self.embed.title and not self.embed.fields:
                self.embed.description = "​"  # Invisible character to satisfy Discord

            return {
                'embed': self.embed if has_embed_content else None,
                'content': self.content if self.content else None,
                'view': self.view
            }
        except Exception as e:
            print(f"Error parsing embed string: {e}")
            # Return basic embed on error
            return {
                'embed': discord.Embed(description="Error parsing embed"),
                'content': None,
                'view': None
            }

    def _split_embed_parts(self, embed_string: str) -> List[str]:
        """Split embed string into parts, handling $v separators properly"""
        # First, let's handle this differently - split by $v first, then process each part
        raw_parts = embed_string.split('$v')
        parts = []

        for raw_part in raw_parts:
            raw_part = raw_part.strip()
            if not raw_part:
                continue

            # Remove outer braces if present
            if raw_part.startswith('{') and raw_part.endswith('}'):
                raw_part = raw_part[1:-1]

            if raw_part.strip():
                parts.append(raw_part.strip())

        return parts

    def _parse_embed_part(self, part: str, buttons: List):
        """Parse individual embed part"""
        part = part.strip()

        if part.startswith('title:'):
            title = part.replace('title:', '').strip()
            # Truncate if too long
            if len(title) > MAX_EMBED_TITLE_LENGTH:
                title = title[:MAX_EMBED_TITLE_LENGTH]
            self.embed.title = title

        elif part.startswith('description:'):
            description = part.replace('description:', '').strip()
            # Truncate if too long
            if len(description) > MAX_EMBED_DESCRIPTION_LENGTH:
                description = description[:MAX_EMBED_DESCRIPTION_LENGTH]
            self.embed.description = description
                
        elif part.startswith('field:'):
            field_content = part.replace('field:', '').strip()
            self._parse_field(field_content)

        elif part.startswith('author:'):
            author_content = part.replace('author:', '').strip()
            self._parse_author(author_content)

        elif part.startswith('footer:'):
            footer_content = part.replace('footer:', '').strip()
            self._parse_footer(footer_content)

        elif part.startswith('thumbnail:'):
            thumbnail = part.replace('thumbnail:', '').strip()
            if thumbnail:
                self.embed.set_thumbnail(url=thumbnail)

        elif part.startswith('image:'):
            image = part.replace('image:', '').strip()
            if image:
                self.embed.set_image(url=image)

        elif part.startswith('color:'):
            color = part.replace('color:', '').strip()
            parsed_color = self._parse_color(color)
            if parsed_color is not None:
                self.embed.color = parsed_color

        elif part.startswith('timestamp'):
            self.embed.timestamp = datetime.now()

        elif part.startswith('url:'):
            url = part.replace('url:', '').strip()
            if url:
                self.embed.url = url

        elif part.startswith('message:') or part.startswith('content:'):
            content = part.replace('message:', '').replace('content:', '').strip()
            self.content = content

        elif part.startswith('button:'):
            button_data = part.replace('button:', '').strip()
            button = self._parse_button(button_data)
            if button:
                buttons.append(button)
    
    def _parse_field(self, field_content: str):
        """Parse field content with both new and legacy syntax"""
        if 'name:' in field_content and 'value:' in field_content:
            # New keyword syntax
            name_match = re.search(r'name:\s*([^&]+?)(?:\s*&&|$)', field_content)
            value_match = re.search(r'value:\s*([^&]+?)(?:\s*&&|$)', field_content)

            name = name_match.group(1).strip() if name_match else ""
            value = value_match.group(1).strip() if value_match else ""
            inline = 'inline' in field_content

            # Placeholders already applied globally

            if name and value:
                self.embed.add_field(name=name, value=value, inline=inline)
        else:
            # Legacy syntax
            parts = field_content.split('&&')
            if len(parts) >= 2:
                name = parts[0].strip()
                value = parts[1].strip()

                # Check for inline - can be 'true', 'inline', or just 'inline' keyword
                inline = False
                if len(parts) > 2:
                    inline_part = parts[2].strip().lower()
                    inline = inline_part in ['true', 'inline'] or 'inline' in field_content
                else:
                    inline = 'inline' in field_content

                # Placeholders already applied globally

                if name and value:
                    self.embed.add_field(name=name, value=value, inline=inline)
    
    def _parse_author(self, author_content: str):
        """Parse author content with both new and legacy syntax"""
        if 'name:' in author_content:
            # New keyword syntax
            name_match = re.search(r'name:\s*([^&]+?)(?:\s*&&|$)', author_content)
            icon_match = re.search(r'icon:\s*([^&]+?)(?:\s*&&|$)', author_content)
            url_match = re.search(r'url:\s*([^&]+?)(?:\s*&&|$)', author_content)

            name = name_match.group(1).strip() if name_match else ""
            icon_url = icon_match.group(1).strip() if icon_match else None
            url = url_match.group(1).strip() if url_match else None

            # Placeholders already applied globally

            if name:
                self.embed.set_author(name=name, icon_url=icon_url, url=url)
        else:
            # Legacy syntax
            parts = author_content.split('&&')
            if len(parts) >= 1:
                name = parts[0].strip()
                icon_url = parts[1].strip() if len(parts) > 1 and parts[1].strip() else None
                url = parts[2].strip() if len(parts) > 2 and parts[2].strip() else None

                # Placeholders already applied globally

                if name:
                    self.embed.set_author(name=name, icon_url=icon_url, url=url)
    
    def _parse_footer(self, footer_content: str):
        """Parse footer content with both new and legacy syntax"""
        if 'text:' in footer_content:
            # New keyword syntax
            text_match = re.search(r'text:\s*([^&]+?)(?:\s*&&|$)', footer_content)
            icon_match = re.search(r'icon:\s*([^&]+?)(?:\s*&&|$)', footer_content)

            text = text_match.group(1).strip() if text_match else ""
            icon_url = icon_match.group(1).strip() if icon_match else None

            # Placeholders already applied globally

            if text:
                self.embed.set_footer(text=text, icon_url=icon_url)
        else:
            # Legacy syntax
            parts = footer_content.split('&&')
            if len(parts) >= 1:
                text = parts[0].strip()
                icon_url = parts[1].strip() if len(parts) > 1 and parts[1].strip() else None

                # Placeholders already applied globally

                if text:
                    self.embed.set_footer(text=text, icon_url=icon_url)
    
    def _parse_color(self, color_str: str) -> Optional[int]:
        """Parse color string to integer using webcolors library"""
        color_str = color_str.strip()

        # Try hex color first
        if color_str.startswith('#'):
            try:
                return int(color_str[1:], 16)
            except ValueError:
                pass

        # Try hex without #
        try:
            return int(color_str, 16)
        except ValueError:
            pass

        # Try named colors using webcolors
        try:
            hex_color = webcolors.name_to_hex(color_str.lower())
            return int(hex_color[1:], 16)  # Convert hex string to int
        except (ValueError, AttributeError):
            pass

        # Return None for invalid colors (Discord will use default)
        return None
    
    def _parse_button(self, button_data: str) -> Optional[discord.ui.Button]:
        """Parse button data"""
        parts = button_data.split('&&')
        if len(parts) < 2:
            return None
        
        button_type = parts[0].strip().lower()
        label = parts[1].strip() if len(parts) > 1 else ""
        
        if button_type == 'link':
            url = parts[2].strip() if len(parts) > 2 else ""
            if not url:
                return None
            
            button = discord.ui.Button(
                style=discord.ButtonStyle.link,
                label=label,
                url=url
            )
        else:
            # Color button - only blue, green, red, gray
            style_map = {
                'blue': discord.ButtonStyle.primary,
                'green': discord.ButtonStyle.success,
                'red': discord.ButtonStyle.danger,
                'grey': discord.ButtonStyle.secondary
            }
            
            style = style_map.get(button_type, discord.ButtonStyle.secondary)
            custom_id = f"button_{datetime.now().timestamp()}_{hash(label)}"
            
            button = discord.ui.Button(
                style=style,
                label=label,
                custom_id=custom_id
            )
        
        return button

    def _apply_placeholders(self, text: str) -> str:
        """Apply user placeholders to text"""
        if not text or not hasattr(self, 'ctx') or not self.ctx:
            return text

        # User placeholders
        text = text.replace('{user.name}', self.ctx.author.display_name)
        text = text.replace('{user.username}', self.ctx.author.name)
        text = text.replace('{user.id}', str(self.ctx.author.id))
        text = text.replace('{user.mention}', self.ctx.author.mention)
        text = text.replace('{user.avatar}', str(self.ctx.author.display_avatar.url))

        # Guild placeholders
        if self.ctx.guild:
            text = text.replace('{guild.name}', self.ctx.guild.name)
            text = text.replace('{guild.id}', str(self.ctx.guild.id))
            text = text.replace('{guild.member_count}', str(self.ctx.guild.member_count))
            if self.ctx.guild.icon:
                text = text.replace('{guild.icon}', str(self.ctx.guild.icon.url))

        # Channel placeholders
        text = text.replace('{channel.name}', self.ctx.channel.name)
        text = text.replace('{channel.id}', str(self.ctx.channel.id))
        text = text.replace('{channel.mention}', self.ctx.channel.mention)

        return text

def create_embed(title: str = None, description: str = None, color: int = None) -> discord.Embed:
    """Create a simple embed"""
    embed = discord.Embed()
    if color is not None:
        embed.color = color
    if title:
        embed.title = title
    if description:
        embed.description = description
    return embed
